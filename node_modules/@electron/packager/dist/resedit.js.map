{"version": 3, "file": "resedit.js", "sourceRoot": "", "sources": ["../src/resedit.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6CAA+B;AAC/B,gDAAgD;AAChD,qCAAkD;AAgBlD;;;GAGG;AACH,SAAS,kBAAkB,CAAC,GAAW;IACrC,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC7B,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC3C,MAAM,IAAI,KAAK,CAAC,0CAA0C,GAAG,yDAAyD,CAAC,CAAC;IAC1H,CAAC;IACD,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;QACxB,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QAClC,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,0CAA0C,GAAG,iBAAiB,IAAI,qCAAqC,CAAC,CAAC;QAC3H,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC,CAA0B,CAAC;AAC9B,CAAC;AAED,6EAA6E;AAC7E,MAAM,gBAAgB,GAAG,EAAE,CAAC;AAErB,KAAK,UAAU,OAAO,CAAC,OAAe,EAAE,OAAoB;IACjE,MAAM,OAAO,GAAG,MAAM,IAAA,UAAW,GAAE,CAAC;IAEpC,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IAC3C,MAAM,GAAG,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC/C,MAAM,GAAG,GAAG,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAEnD,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;QACrB,YAAY;QACZ,MAAM,kBAAkB,GAAG,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACpF,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,kFAAkF,CAAC,CAAC;QACtG,CAAC;QACD,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;QACjF,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,uBAAuB,CACrD,GAAG,CAAC,OAAO,EACX,kBAAkB,CAAC,CAAC,CAAC,CAAC,EAAE,EACxB,kBAAkB,CAAC,CAAC,CAAC,CAAC,IAAI,EAC1B,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CACxC,CAAC;IACJ,CAAC;IAED,WAAW;IACX,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC,sBAAsB,CAAC,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC,2BAA2B,CAAC,EAAE,CAAC;QAC5G,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC,sBAAsB,CAAC,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC,2BAA2B,CAAC,EAAE,CAAC;YAC5G,MAAM,IAAI,KAAK,CAAC,6FAA6F,CAAC,CAAC;QACjH,CAAC;QAED,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,gBAAgB,CAAC,CAAC;QACvE,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,gFAAgF,CAAC,CAAC;QACpG,CAAC;QACD,MAAM,aAAa,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC,sBAAsB,CAAC,EAAE,CAAC;YACpD,aAAa,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAClG,CAAC;aAAM,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC,2BAA2B,CAAC,EAAE,CAAC;YAChE,kFAAkF;YAClF,qEAAqE;YACrE,MAAM,sBAAsB,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAChF,MAAM,UAAU,GAAG,sBAAsB,CAAC,OAAO,CAC/C,qEAAqE,EACrE,KAAK,OAAO,CAAC,aAAa,EAAE,CAAC,2BAA2B,CAAC,IAAI,CAC9D,CAAC;YACF,aAAa,CAAC,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED,eAAe;IACf,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IAC1E,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC7B,MAAM,IAAI,KAAK,CAAC,oFAAoF,CAAC,CAAC;IACxG,CAAC;IACD,IAAI,OAAO,CAAC,WAAW;QAAE,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,GAAG,kBAAkB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;IACnG,IAAI,OAAO,CAAC,cAAc;QAAE,WAAW,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,GAAG,kBAAkB,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC;IAC5G,MAAM,YAAY,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,8BAA8B,EAAE,CAAC;IACrE,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC9B,MAAM,IAAI,KAAK,CAAC,qFAAqF,CAAC,CAAC;IACzG,CAAC;IACD,sCAAsC;IACtC,MAAM,UAAU,GAA2B;QACzC,WAAW,EAAE,OAAO,CAAC,aAAa,EAAE,WAAW,IAAI,EAAE;QACrD,eAAe,EAAE,OAAO,CAAC,aAAa,EAAE,eAAe,IAAI,EAAE;QAC7D,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,EAAE;QACtC,YAAY,EAAE,OAAO,CAAC,aAAa,EAAE,YAAY,IAAI,EAAE;QACvD,cAAc,EAAE,OAAO,CAAC,cAAc,IAAI,EAAE;QAC5C,gBAAgB,EAAE,OAAO,CAAC,aAAa,EAAE,gBAAgB,IAAI,EAAE;QAC/D,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,EAAE;QACtC,cAAc,EAAE,OAAO,CAAC,cAAc,IAAI,EAAE;KAC7C,CAAC;IACF,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;QAC1C,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;YAAE,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC;IAC/C,CAAC;IACD,WAAW,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;IAE5D,sBAAsB;IACtB,WAAW,CAAC,CAAC,CAAC,CAAC,uBAAuB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IAEpD,iBAAiB;IACjB,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;QAC1B,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YACtE,IAAI;YACJ,GAAG,EAAE,OAAO,CAAC,aAAc,CAAC,IAAI,CAAC,CAAC,SAAS;YAC3C,KAAK,EAAE,OAAO,CAAC,aAAc,CAAC,IAAI,CAAC,CAAC,IAAI;SACzC,CAAC,CAAC,CAAC;QACJ,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC;YACf,IAAI,EAAE,WAAW;YACjB,EAAE,EAAE,cAAc;YAClB,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,OAAO,CAAC;YACxD,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI;YAC1B,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ;SACnC,CAAC,CAAC;IACL,CAAC;IAED,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;IAExB,MAAM,EAAE,CAAC,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;AAC3D,CAAC;AAhGD,0BAgGC"}