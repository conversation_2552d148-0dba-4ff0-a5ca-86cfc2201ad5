"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.serialHooks = exports.packager = exports.allOfficialArchsForPlatformAndVersion = void 0;
const hooks_1 = require("./hooks");
Object.defineProperty(exports, "serialHooks", { enumerable: true, get: function () { return hooks_1.serialHooks; } });
const packager_1 = require("./packager");
Object.defineProperty(exports, "packager", { enumerable: true, get: function () { return packager_1.packager; } });
const targets_1 = require("./targets");
Object.defineProperty(exports, "allOfficialArchsForPlatformAndVersion", { enumerable: true, get: function () { return targets_1.allOfficialArchsForPlatformAndVersion; } });
exports.default = packager_1.packager;
__exportStar(require("./types"), exports);
module.exports = packager_1.packager;
module.exports.allOfficialArchsForPlatformAndVersion = targets_1.allOfficialArchsForPlatformAndVersion;
module.exports.packager = packager_1.packager;
module.exports.serialHooks = hooks_1.serialHooks;
module.exports.default = packager_1.packager;
//# sourceMappingURL=index.js.map