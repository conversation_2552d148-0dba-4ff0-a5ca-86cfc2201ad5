{"version": 3, "file": "common.js", "sourceRoot": "", "sources": ["../src/common.ts"], "names": [], "mappings": ";;;;;;AAAA,4DAAoC;AACpC,wDAA0B;AAC1B,4CAAoB;AACpB,gDAAwB;AACxB,kDAAgC;AAInB,QAAA,KAAK,GAAG,IAAA,eAAW,EAAC,mBAAmB,CAAC,CAAC;AAEtD,SAAgB,eAAe,CAAC,IAAY;IAC1C,OAAO,IAAA,oBAAU,EAAC,IAAI,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC,CAAC;AAChD,CAAC;AAFD,0CAEC;AAED,SAAgB,qBAAqB,CAAC,IAAsD;IAC1F,OAAO,GAAG,eAAe,CAAC,IAAI,CAAC,IAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;AACxE,CAAC;AAFD,sDAEC;AAED,SAAgB,iBAAiB,CAAC,IAAkB;IAClD,OAAO,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC;AAC3E,CAAC;AAFD,8CAEC;AAED,SAAgB,IAAI,CAAC,OAAgB,EAAE,KAAe;IACpD,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACxB,CAAC;AACH,CAAC;AAJD,oBAIC;AAED,SAAgB,OAAO,CAAC,OAAgB,EAAE,KAAe;IACvD,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,OAAO,CAAC,IAAI,CAAC,YAAY,OAAO,EAAE,CAAC,CAAC;IACtC,CAAC;AACH,CAAC;AAJD,0BAIC;AAED,SAAgB,gBAAgB,CAAC,UAAmC,EAAE,UAAkB,EAAE,SAAiB,EAAE,KAAc,EAAE,KAAe;IAC1I,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,SAAS,CAAC,EAAE,CAAC;QAChE,OAAO,CAAC,GAAG,UAAU,IAAI,SAAS,yCAAyC,EAAE,KAAK,CAAC,CAAC;IACtF,CAAC;IACD,UAAU,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAChC,CAAC;AALD,4CAKC;AAED,SAAgB,cAAc,CAAC,IAAkB;IAC/C,IAAI,WAAW,CAAC;IAChB,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;QACvB,WAAW,GAAG,EAAE,CAAC;IACnB,CAAC;SAAM,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;QACzC,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC;IAC1B,CAAC;SAAM,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;QAC1D,OAAO,KAAK,CAAC;IACf,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,2CAA2C,IAAI,CAAC,IAAI,gCAAgC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1G,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,WAAW,CAAC;AACrB,CAAC;AAdD,wCAcC;AAED,SAAgB,WAAW,CAAI,KAAc;IAC3C,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AAChD,CAAC;AAFD,kCAEC;AAED,SAAgB,aAAa,CAAC,QAAkC;IAC9D,OAAO,QAAQ,KAAK,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AACrD,CAAC;AAFD,sCAEC;AAED,SAAgB,WAAW,CAAC,IAAa;IACvC,OAAO,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,YAAE,CAAC,MAAM,EAAE,EAAE,mBAAmB,CAAC,CAAC;AACpE,CAAC;AAFD,kCAEC;AAED;;GAEG;AACH,SAAgB,aAAa,CAAC,eAAuB;IACnD,OAAO,eAAe,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAC7C,CAAC;AAFD,sCAEC;AAED;;;;;;;;GAQG;AACI,KAAK,UAAU,mBAAmB,CAAC,MAAc,EAAE,aAAqB;IAC7E,IAAA,aAAK,EAAC,iCAAiC,CAAC,CAAC;IACzC,IAAA,aAAK,EAAC,kCAAkC,CAAC,CAAC;IAE1C,MAAM,sBAAsB,GAAG,cAAI,CAAC,IAAI,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;IACxE,IAAI,CAAC,CAAC,MAAM,kBAAE,CAAC,UAAU,CAAC,sBAAsB,CAAC,CAAC,EAAE,CAAC;QACnD,MAAM,uBAAuB,GAAG,cAAI,CAAC,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;QAClE,MAAM,IAAI,KAAK,CAAC,kDAAkD,uBAAuB,yDAAyD,CAAC,CAAC;IACtJ,CAAC;IAED,IAAA,aAAK,EAAC,wCAAwC,CAAC,CAAC;IAChD,MAAM,WAAW,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;IAC9D,MAAM,kBAAkB,GAAG,WAAW,CAAC,IAAI,IAAI,UAAU,CAAC;IAC1D,MAAM,UAAU,GAAG,cAAI,CAAC,OAAO,CAAC,aAAa,EAAE,kBAAkB,CAAC,CAAC;IACnE,IAAI,CAAC,CAAC,MAAM,kBAAE,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC;QACvC,MAAM,kBAAkB,GAAG,cAAI,CAAC,IAAI,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAC;QACjE,MAAM,IAAI,KAAK,CAAC,8DAA8D,kBAAkB,yDAAyD,CAAC,CAAC;IAC7J,CAAC;IAED,IAAA,aAAK,EAAC,qBAAqB,CAAC,CAAC;AAC/B,CAAC;AApBD,kDAoBC;AAED,SAAgB,QAAQ;IACtB,8DAA8D;IAC9D,MAAM,QAAQ,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC;IAE5C,OAAO,qBAAqB,QAAQ,CAAC,OAAO,IAAI;QAC9C,QAAQ,OAAO,CAAC,OAAO,IAAI;QAC3B,0BAA0B,OAAO,CAAC,QAAQ,IAAI,YAAE,CAAC,OAAO,EAAE,KAAK,OAAO,CAAC,IAAI,GAAG,CAAC;AACnF,CAAC;AAPD,4BAOC"}