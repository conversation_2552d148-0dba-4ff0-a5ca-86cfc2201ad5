{"version": 3, "file": "infer.js", "sourceRoot": "", "sources": ["../src/infer.ts"], "names": [], "mappings": ";;;;;;AAAA,wEAA6H;AAC7H,gEAAuC;AACvC,gDAAwB;AACxB,sDAA6C;AAC7C,qCAAiC;AAGjC,SAAS,yBAAyB,CAAC,KAAe;IAChD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,aAAa,IAAI,IAAI,KAAK,uBAAuB,CAAC,CAAC;AACxF,CAAC;AAED,SAAS,uBAAuB,CAAC,IAAY;IAC3C,IAAI,IAAI,EAAE,eAAe,CAAC;IAC1B,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,aAAa;YAChB,IAAI,GAAG,MAAM,CAAC;YACd,eAAe,GAAG,kBAAkB,CAAC;YACrC,MAAM;QACR,KAAK,uBAAuB;YAC1B,IAAI,GAAG,iBAAiB,CAAC;YACzB,eAAe,GAAG,kBAAkB,CAAC;YACrC,MAAM;QACR,KAAK,SAAS;YACZ,IAAI,GAAG,YAAY,CAAC;YACpB,eAAe,GAAG,qBAAqB,CAAC;YACxC,MAAM;QACR,0BAA0B;QAC1B;YACE,IAAI,GAAG,EAAE,CAAC;YACV,eAAe,GAAG,sBAAsB,IAAI,IAAI,CAAC;IACrD,CAAC;IAED,OAAO,uBAAuB,eAAe,uBAAuB,eAAe,MAAM;QACvF,oCAAoC;QACpC,oEAAoE,IAAI,IAAI,CAAC;AACjF,CAAC;AAED,SAAS,cAAc,CAAC,EAAU,EAAE,OAAkB;IACpD,+CAA+C;IAC/C,OAAO,IAAI,OAAO,CAA4C,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;QAC/E,IAAA,iBAAO,EAAC,EAAE,EAAE,OAAO,EAAE,CAAC,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,EAAE;YAC1C,IAAI,GAAG,EAAE,CAAC;gBACR,0BAA0B;gBAC1B,MAAM,CAAC,GAAG,CAAC,CAAC;YACd,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,CAAC,QAA8B,EAAE,GAA0B,CAAC,CAAC,CAAC;YACvE,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAED,KAAK,UAAU,UAAU,CAAC,IAAa,EAAE,YAA4C;IACnF,MAAM,CAAC,EAAE,WAAW,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACrD,MAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC;IAE7B,MAAM,GAAG,GAAG,CAAC,MAAM,cAAc,CAAC,WAAW,EAAE,EAAE,OAAO,EAAE,cAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnF,IAAA,cAAK,EAAC,0CAA0C,WAAW,OAAO,GAAG,EAAE,CAAC,CAAC;IACzE,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC,OAAO,CAAC;AACrC,CAAC;AAED,KAAK,UAAU,cAAc,CAAC,IAAa,EAAE,MAA4B;IACvE,IAAI,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;QAC9B,IAAA,cAAK,EAAC,mCAAmC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;QAC/G,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,WAAqB,CAAC;IAClD,CAAC;IAED,IAAI,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QAC1B,IAAA,cAAK,EAAC,wCAAwC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;QAC3E,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,OAAiB,CAAC;IACpD,CAAC;IAED,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;QAChD,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;IAC1B,CAAC;IAED,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QACzB,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,MAAmC,CAAC;QAEjE,IAAA,cAAK,EAAC,sDAAsD,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;QACxF,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC/B,IAAI,CAAC,aAAc,CAAC,WAAW,GAAG,IAAA,sBAAW,EAAC,MAAM,CAAC,CAAC,IAAI,CAAC;QAC7D,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;YACvB,IAAI,CAAC,aAAc,CAAC,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC;QAChD,CAAC;aAAM,CAAC;YACN,IAAA,cAAK,EAAC,mEAAmE,CAAC,CAAC;QAC7E,CAAC;IACH,CAAC;IAED,iDAAiD;IACjD,IAAI,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,uBAAuB,CAAC,EAAE,CAAC;QAC1D,OAAO,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,uBAAuB,CAAC,CAAC,CAAC;IAClE,CAAC;SAAM,CAAC;QACN,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;AACH,CAAC;AAED,SAAS,uBAAuB,CAAC,IAAa,EAAE,GAAwB;IACtE,MAAM,YAAY,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;QAC/C,OAAO,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9C,CAAC,CAAC,CAAC;IAEH,IAAI,yBAAyB,CAAC,YAAY,CAAC,EAAE,CAAC;QAC5C,MAAM,QAAQ,GAAG,YAAY,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QAE3D,IAAA,cAAK,EAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACnB,GAAG,CAAC,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;QACzC,MAAM,GAAG,CAAC;IACZ,CAAC;SAAM,CAAC;QACN,6DAA6D;QAC7D,OAAO,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;IAC1C,CAAC;AACH,CAAC;AAEM,KAAK,UAAU,0BAA0B,CAAC,SAA8B,EAAE,IAAa,EAAE,GAAW;IACzG,MAAM,KAAK,GAA6B,EAAE,CAAC;IAE3C,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QACf,KAAK,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC;IACtC,CAAC;IAED,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;QACrB,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACxB,CAAC;IAED,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;QAC1B,KAAK,CAAC,IAAI,CAAC;YACT,uBAAuB;YACvB,0BAA0B;YAC1B,+BAA+B;YAC/B,kCAAkC;SACnC,CAAC,CAAC;IACL,CAAC;IAED,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,EAAE,CAAC;QAC3F,IAAA,cAAK,EAAC,sFAAsF,CAAC,CAAC;QAC9F,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACvB,CAAC;IAED,8CAA8C;IAC9C,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACvB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED,2DAA2D;IAC3D,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,IAAA,0BAAc,EAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAChD,OAAO,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACtC,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,GAAG,GAAG,CAAwB,CAAC;QAErC,IAAI,GAAG,CAAC,YAAY,EAAE,CAAC;YACrB,IAAI,GAAG,CAAC,YAAY,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,EAAE,CAAC;gBAC7C,IAAA,cAAK,EAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBACnB,GAAG,CAAC,OAAO,GAAG,4CAA4C,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,8EAA8E,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9L,CAAC;iBAAM,CAAC;gBACN,OAAO,uBAAuB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YAC5C,CAAC;QACH,CAAC;QAED,MAAM,GAAG,CAAC;IACZ,CAAC;AACH,CAAC;AAhDD,gEAgDC"}