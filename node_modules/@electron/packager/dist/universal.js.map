{"version": 3, "file": "universal.js", "sourceRoot": "", "sources": ["../src/universal.ts"], "names": [], "mappings": ";;;;;;AAAA,mDAAuD;AACvD,qCAAmD;AACnD,wDAA0B;AAC1B,gDAAwB;AACxB,+BAA4B;AAIrB,KAAK,UAAU,mBAAmB,CAAC,iCAAgF,EACxH,QAAgB,EAAE,SAAuB,EACzC,YAA6B,EAAE,QAAgB;IAC/C,mGAAmG;IACnG,8BAA8B;IAC9B,IAAA,aAAI,EAAC,8BAA8B,SAAS,CAAC,QAAQ,8BAA8B,SAAS,CAAC,eAAe,sCAAsC,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;IACrK,MAAM,kBAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC1B,MAAM,OAAO,GAAG,MAAM,kBAAE,CAAC,OAAO,CAAC,cAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,8BAA8B,CAAC,CAAC,CAAC;IAEzF,MAAM,GAAG,GAAG,IAAI,SAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IACzC,MAAM,oBAAoB,GAAG,GAAG,CAAC,WAAW,CAAC;IAC7C,MAAM,kBAAkB,GAAG,IAAA,0BAAiB,EAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAEvD,IAAI,MAAM,kBAAE,CAAC,UAAU,CAAC,kBAAkB,CAAC,EAAE,CAAC;QAC5C,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC;YACxB,MAAM,kBAAE,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;QACtC,CAAC;aAAM,CAAC;YACN,IAAA,aAAI,EAAC,YAAY,SAAS,CAAC,QAAQ,IAAI,SAAS,CAAC,IAAI,wDAAwD,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;YAChI,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,MAAM,YAAY,GAAG,EAAmC,CAAC;IAEzD,MAAM,OAAO,CAAC,GAAG,CAAE,CAAC,KAAK,EAAE,OAAO,CAAqB,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;QAC7E,MAAM,QAAQ,GAAG;YACf,GAAG,SAAS;YACZ,IAAI,EAAE,QAAQ;YACd,GAAG,EAAE,OAAO;SACb,CAAC;QACF,MAAM,gBAAgB,GAAG;YACvB,GAAG,YAAY;YACf,IAAI,EAAE,QAAQ;SACf,CAAC;QACF,2FAA2F;QAC3F,OAAO,QAAQ,CAAC,OAAO,CAAC;QACxB,OAAO,QAAQ,CAAC,WAAW,CAAC;QAE5B,yHAAyH;QACzH,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,iCAAiC,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAW,CAAC;IAC3G,CAAC,CAAC,CAAC,CAAC;IAEJ,MAAM,UAAU,GAAG,YAAY,CAAC,GAAG,CAAC;IACpC,MAAM,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC;IAExC,IAAA,aAAI,EAAC,wCAAwC,SAAS,CAAC,QAAQ,EAAE,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;IAEpF,MAAM,cAAc,GAAG,MAAM,kBAAE,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IACpD,MAAM,OAAO,GAAG,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAEhF,MAAM,IAAA,4BAAgB,EAAC;QACrB,GAAG,SAAS,CAAC,YAAY;QACzB,UAAU,EAAE,cAAI,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC;QAC7C,YAAY,EAAE,cAAI,CAAC,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC;QACjD,UAAU,EAAE,cAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,OAAO,CAAC;QACvD,KAAK,EAAE,KAAK;KACb,CAAC,CAAC;IAEH,MAAM,GAAG,CAAC,kBAAkB,EAAE,CAAC;IAC/B,MAAM,GAAG,CAAC,sBAAsB,EAAE,CAAC;IACnC,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC;IAEjB,KAAK,MAAM,aAAa,IAAI,cAAc,EAAE,CAAC;QAC3C,IAAI,cAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,MAAM,EAAE,CAAC;YAC3C,SAAS;QACX,CAAC;QAED,MAAM,kBAAE,CAAC,IAAI,CAAC,cAAI,CAAC,OAAO,CAAC,UAAU,EAAE,aAAa,CAAC,EAAE,cAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC,CAAC;IAC1G,CAAC;IAED,MAAM,kBAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAEzB,OAAO,kBAAkB,CAAC;AAC5B,CAAC;AAzED,kDAyEC"}