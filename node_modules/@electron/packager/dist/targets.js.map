{"version": 3, "file": "targets.js", "sourceRoot": "", "sources": ["../src/targets.ts"], "names": [], "mappings": ";;;;;;AAAA,qCAAmC;AACnC,uCAA4C;AAC5C,oDAA4B;AAGf,QAAA,aAAa,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;AAE5E,QAAA,iBAAiB,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AAExD,QAAA,0BAA0B,GAAG;IACxC,MAAM,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,WAAW,CAAC;IACrC,KAAK,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,CAAC;IACrD,GAAG,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,WAAW,CAAC;IAClC,KAAK,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC;CACc,CAAC;AAEhD,MAAM,aAAa,GAAG;IACpB,MAAM,EAAE;QACN,KAAK,EAAE,kBAAkB;QACzB,SAAS,EAAE,kBAAkB;KAC9B;IACD,KAAK,EAAE;QACL,KAAK,EAAE,UAAU;QACjB,IAAI,EAAE,gBAAgB;QACtB,QAAQ,EAAE,eAAe;KAC1B;IACD,GAAG,EAAE;QACH,KAAK,EAAE,kBAAkB;QACzB,SAAS,EAAE,kBAAkB;KAC9B;IACD,KAAK,EAAE;QACL,KAAK,EAAE,UAAU;KAClB;CAC0D,CAAC;AAE9D,oEAAoE;AACvD,QAAA,SAAS,GAAqC;IACzD,MAAM,EAAE,OAAO;IACf,KAAK,EAAE,SAAS;IAChB,GAAG,EAAE,OAAO,EAAE,gBAAgB;IAC9B,KAAK,EAAE,SAAS;CACjB,CAAC;AAEW,QAAA,SAAS,GAAG;IACvB,IAAI,EAAE,IAAI,GAAG,CAAC,qBAAa,CAAC;IAC5B,QAAQ,EAAE,IAAI,GAAG,CAAC,yBAAiB,CAAC;CACrC,CAAC;AAEF,SAAgB,uBAAuB,CAAC,IAAa,EAAE,iBAAsC,EAC3F,aAA8B,EAC9B,UAAuB;IACvB,MAAM,YAAY,GAA8C,EAAE,CAAC;IAEnE,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE,CAAC;QACjC,KAAK,MAAM,QAAQ,IAAI,iBAAiB,EAAE,CAAC;YACzC,IAAI,6BAA6B,CAAC,IAAI,CAAC,EAAE,CAAC;gBACxC,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC;oBAC/C,qBAAqB,CAAC,IAAI,EAAE,iCAAiC,QAAQ,IAAI,IAAI,kDAAkD,CAAC,CAAC;oBACjI,SAAS;gBACX,CAAC;qBAAM,IAAI,aAAa,CAAC,QAAQ,CAAC,IAAI,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;oBACpE,MAAM,YAAY,GAAG,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC;oBACnD,IAAI,YAAY,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,YAAY,CAAC,EAAE,CAAC;wBAC7D,qBAAqB,CAAC,IAAI,EAAE,YAAY,QAAQ,IAAI,IAAI,oCAAoC,YAAY,EAAE,CAAC,CAAC;wBAC5G,SAAS;oBACX,CAAC;gBACH,CAAC;gBAED,IAAI,OAAO,UAAU,KAAK,UAAU,IAAI,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC;oBACnE,SAAS;gBACX,CAAC;YACH,CAAC;YACD,YAAY,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAED,OAAO,YAAY,CAAC;AACtB,CAAC;AA5BD,0DA4BC;AAED,SAAS,qBAAqB,CAAC,IAA4B,EAAE,KAAc,EAAE,eAA4B;IACvG,OAAO,IAAI,KAAK,CAAC,eAAe,IAAI,IAAI,KAAK,KAAK,OAAO,KAAK,iCAAiC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;SAChI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACnB,CAAC;AAED,SAAS,6BAA6B,CAAC,IAAa;IAClD,OAAO,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;AACjG,CAAC;AAED,SAAS,yBAAyB,CAAC,QAA2B,EAAE,IAAmB;IACjF,OAAO,kCAA0B,CAAC,QAAQ,CAAC,IAAI,kCAA0B,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACrG,CAAC;AAED,SAAS,mBAAmB,CAAC,IAAsC,EAAE,YAAoB;IACvF,OAAO,gBAAM,CAAC,SAAS,CAAC,IAAI,CAAC,eAAgB,EAAE,YAAY,EAAE,EAAE,iBAAiB,EAAE,IAAI,EAAE,CAAC,CAAC;AAC5F,CAAC;AAED,SAAS,4BAA4B,CAAC,IAAa;IACjD,OAAO,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,CAAC;AACpE,CAAC;AAED,SAAS,qBAAqB,CAAC,IAAa,EAAE,OAAe;IAC3D,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,EAAE,CAAC;QACxC,IAAA,gBAAO,EAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;AACH,CAAC;AAED,SAAgB,qCAAqC,CAAC,QAA2B,EAC/E,eAA2C;IAC3C,MAAM,KAAK,GAAG,kCAA0B,CAAC,QAAQ,CAAC,CAAC;IAEnD,IAAI,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC5B,MAAM,aAAa,GAAI,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAqB;aAC5E,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,mBAAmB,CAAC,EAAE,eAAe,EAAE,eAAe,EAAE,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7G,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;IAC7D,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAXD,sFAWC;AAED,gDAAgD;AAChD,gEAAgE;AAChE,SAAgB,uBAAuB,CAAC,IAAa,EAAE,IAA4B;IACjF,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,OAAO,KAAK,CAAC,IAAI,CAAC,iBAAS,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;IAC9C,CAAC;IAED,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;IACtB,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,IAAI,IAAI,KAAK,MAAM,EAAE,CAAC;YACpB,IAAI,GAAG,IAAA,iBAAW,GAAE,CAAC;QACvB,CAAC;aAAM,CAAC;YACN,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QACvB,CAAC;IACH,CAAC;SAAM,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC;QAC1B,OAAO,KAAK,CAAC,IAAI,CAAC,iBAAS,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;IAC9C,CAAC;IAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;QACzB,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC7B,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAC5B,CAAC;aAAM,CAAC;YACN,OAAO,qBAAqB,CAAC,IAAI,EAAE,IAAI,EAAE,iBAAS,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED,MAAM,wBAAwB,GAAG,6BAA6B,CAAC,IAAI,CAAC,CAAC;IAErE,KAAK,MAAM,KAAK,IAAI,IAAI,EAAE,CAAC;QACzB,IAAI,wBAAwB,IAAI,CAAC,iBAAS,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YAC5D,OAAO,qBAAqB,CAAC,IAAI,EAAE,KAAK,EAAE,iBAAS,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAjCD,0DAiCC"}