{"version": 3, "file": "prune.js", "sourceRoot": "", "sources": ["../src/prune.ts"], "names": [], "mappings": ";;;;;;AAAA,qCAAkD;AAClD,uCAA0E;AAC1E,wDAA0B;AAC1B,gDAAwB;AAExB,MAAM,gBAAgB,GAAG;IACvB,UAAU;IACV,kBAAkB;CACnB,CAAC;AAEF,MAAa,MAAM;IAOjB,YAAY,GAAW,EAAE,KAAc;QAJvC,YAAO,GAAG,IAAI,GAAG,EAAU,CAAC;QAE5B,eAAU,GAAG,KAAK,CAAC;QAGjB,IAAI,CAAC,OAAO,GAAG,IAAA,sBAAa,EAAC,GAAG,CAAC,CAAC;QAClC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,IAAI,6BAAkB,CAAC;YACrC,aAAa,EAAE,GAAG;YAClB,oBAAoB,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC;SACpF,CAAC,CAAC;IACL,CAAC;IAED,UAAU,CAAC,SAAoB;QAC7B,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,IAAI,IAAA,sBAAa,EAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QACpG,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC;QACpC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,IAAY;QAC5B,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QACvC,CAAC;aAAM,CAAC;YACN,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;YAClF,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YAC3B,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAED,gBAAgB,CAAC,MAAc,EAAE,QAAiB;QAChD,IAAI,QAAQ,IAAI,MAAM,CAAC,OAAO,KAAK,kBAAO,CAAC,IAAI,EAAE,CAAC;YAChD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3C,IAAA,gBAAO,EAAC,UAAU,MAAM,CAAC,IAAI,8CAA8C,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YACzF,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,kBAAkB,CAAC,IAAY;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;CACF;AAhDD,wBAgDC;AAED,SAAS,kBAAkB,CAAC,WAAmB;IAC7C,OAAO,cAAI,CAAC,QAAQ,CAAC,cAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,KAAK,cAAc;QAChE,CAAC,cAAI,CAAC,QAAQ,CAAC,cAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,cAAI,CAAC,QAAQ,CAAC,cAAI,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,cAAI,CAAC,GAAG,IAAI,CAAC,CAAC,KAAK,cAAc,CAAC,CAAC;AACjJ,CAAC;AAEM,KAAK,UAAU,QAAQ,CAAC,WAAmB;IAChD,OAAO,CAAC,MAAM,kBAAE,CAAC,UAAU,CAAC,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC,CAAC,IAAI,kBAAkB,CAAC,WAAW,CAAC,CAAC;AAC1G,CAAC;AAFD,4BAEC"}