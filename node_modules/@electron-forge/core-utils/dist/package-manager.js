"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.spawnPackageManager = exports.resolvePackageManager = exports.PACKAGE_MANAGERS = void 0;
const cross_spawn_promise_1 = require("@malept/cross-spawn-promise");
const chalk_1 = __importDefault(require("chalk"));
const debug_1 = __importDefault(require("debug"));
const find_up_1 = __importDefault(require("find-up"));
const log_symbols_1 = __importDefault(require("log-symbols"));
const d = (0, debug_1.default)('electron-forge:package-manager');
let hasWarned = false;
/**
 * Supported package managers and the commands and flags they need to install dependencies.
 */
exports.PACKAGE_MANAGERS = {
    yarn: {
        executable: 'yarn',
        install: 'add',
        dev: '--dev',
        exact: '--exact',
    },
    npm: {
        executable: 'npm',
        install: 'install',
        dev: '--save-dev',
        exact: '--save-exact',
    },
    pnpm: {
        executable: 'pnpm',
        install: 'add',
        dev: '--save-dev',
        exact: '--save-exact',
    },
};
const PM_FROM_LOCKFILE = {
    'package-lock.json': 'npm',
    'yarn.lock': 'yarn',
    'pnpm-lock.yaml': 'pnpm',
};
/**
 * Parses the `npm_config_user_agent` environment variable and returns its name and version.
 *
 * Code taken from {@link https://github.com/zkochan/packages/tree/main/which-pm-runs/ | which-pm-runs}.
 */
function pmFromUserAgent() {
    const userAgent = process.env.npm_config_user_agent;
    if (!userAgent) {
        return undefined;
    }
    const pmSpec = userAgent.split(' ', 1)[0];
    const separatorPos = pmSpec.lastIndexOf('/');
    const name = pmSpec.substring(0, separatorPos);
    return {
        name: name === 'npminstall' ? 'cnpm' : name,
        version: pmSpec.substring(separatorPos + 1),
    };
}
/**
 * Resolves the package manager to use. In order, it checks the following:
 *
 * 1. The value of the `NODE_INSTALLER` environment variable.
 * 2. The `process.env.npm_config_user_agent` value set by the executing package manager.
 * 3. The presence of a lockfile in an ancestor directory.
 * 4. If an unknown package manager is used (or none of the above apply), then we fall back to `npm`.
 *
 * The version of the executing package manager is also returned if it is detected via user agent.
 *
 * Supported package managers are `yarn`, `pnpm`, and `npm`.
 *
 */
const resolvePackageManager = async () => {
    const executingPM = pmFromUserAgent();
    const lockfile = await (0, find_up_1.default)(['package-lock.json', 'yarn.lock', 'pnpm-lock.yaml', 'pnpm-workspace.yaml'], { type: 'file' });
    const lockfilePM = (lockfile && PM_FROM_LOCKFILE[lockfile]) ?? undefined;
    const installer = process.env.NODE_INSTALLER || executingPM?.name || lockfilePM;
    // TODO(erickzhao): Remove NODE_INSTALLER environment variable for Forge 8
    if (typeof process.env.NODE_INSTALLER === 'string' && !hasWarned) {
        console.warn(log_symbols_1.default.warning, chalk_1.default.yellow(`The NODE_INSTALLER environment variable is deprecated and will be removed in Electron Forge v8`));
        hasWarned = true;
    }
    switch (installer) {
        case 'yarn':
        case 'npm':
        case 'pnpm':
            d(`Resolved package manager to ${installer}. (Derived from NODE_INSTALLER: ${process.env.NODE_INSTALLER}, npm_config_user_agent: ${executingPM}, lockfile: ${lockfilePM}.)`);
            return { ...exports.PACKAGE_MANAGERS[installer], version: executingPM?.version };
        default:
            if (installer !== undefined) {
                console.warn(log_symbols_1.default.warning, chalk_1.default.yellow(`Package manager ${chalk_1.default.red(installer)} is unsupported. Falling back to ${chalk_1.default.green('npm')} instead.`));
            }
            else {
                d(`No package manager detected. Falling back to npm.`);
            }
            return exports.PACKAGE_MANAGERS['npm'];
    }
};
exports.resolvePackageManager = resolvePackageManager;
const spawnPackageManager = async (pm, args, opts) => {
    return (0, cross_spawn_promise_1.spawn)(pm.executable, args, opts);
};
exports.spawnPackageManager = spawnPackageManager;
//# sourceMappingURL=data:application/json;base64,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