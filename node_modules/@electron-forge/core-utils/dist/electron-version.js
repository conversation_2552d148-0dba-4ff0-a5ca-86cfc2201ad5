"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateElectronDependency = exports.getElectronVersion = exports.getElectronModulePath = exports.PackageNotFoundError = void 0;
const node_path_1 = __importDefault(require("node:path"));
const debug_1 = __importDefault(require("debug"));
const find_up_1 = __importDefault(require("find-up"));
const fs_extra_1 = __importDefault(require("fs-extra"));
const semver_1 = __importDefault(require("semver"));
const d = (0, debug_1.default)('electron-forge:electron-version');
const electronPackageNames = ['electron-nightly', 'electron'];
function findElectronDep(dep) {
    return electronPackageNames.includes(dep);
}
async function findAncestorNodeModulesPath(dir, packageName) {
    d('Looking for a lock file to indicate the root of the repo');
    const lockPath = await (0, find_up_1.default)(['package-lock.json', 'yarn.lock', 'pnpm-lock.yaml'], { cwd: dir, type: 'file' });
    if (lockPath) {
        d(`Found lock file: ${lockPath}`);
        const nodeModulesPath = node_path_1.default.join(node_path_1.default.dirname(lockPath), 'node_modules', packageName);
        if (await fs_extra_1.default.pathExists(nodeModulesPath)) {
            return nodeModulesPath;
        }
    }
    return Promise.resolve(undefined);
}
async function determineNodeModulesPath(dir, packageName) {
    const nodeModulesPath = node_path_1.default.join(dir, 'node_modules', packageName);
    if (await fs_extra_1.default.pathExists(nodeModulesPath)) {
        return nodeModulesPath;
    }
    return findAncestorNodeModulesPath(dir, packageName);
}
class PackageNotFoundError extends Error {
    constructor(packageName, dir) {
        super(`Cannot find the package "${packageName}". Perhaps you need to run install it in "${dir}"?`);
    }
}
exports.PackageNotFoundError = PackageNotFoundError;
function getElectronModuleName(packageJSON) {
    if (!packageJSON.devDependencies) {
        throw new Error('package.json for app does not have any devDependencies');
    }
    // Why: checked above
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const packageName = electronPackageNames.find((pkg) => packageJSON.devDependencies[pkg]);
    if (packageName === undefined) {
        throw new Error('Could not find any Electron packages in devDependencies');
    }
    return packageName;
}
async function getElectronPackageJSONPath(dir, packageName) {
    const nodeModulesPath = await determineNodeModulesPath(dir, packageName);
    if (!nodeModulesPath) {
        throw new PackageNotFoundError(packageName, dir);
    }
    const electronPackageJSONPath = node_path_1.default.join(nodeModulesPath, 'package.json');
    if (await fs_extra_1.default.pathExists(electronPackageJSONPath)) {
        return electronPackageJSONPath;
    }
    return undefined;
}
async function getElectronModulePath(dir, packageJSON) {
    const moduleName = getElectronModuleName(packageJSON);
    const packageJSONPath = await getElectronPackageJSONPath(dir, moduleName);
    if (packageJSONPath) {
        return node_path_1.default.dirname(packageJSONPath);
    }
    return undefined;
}
exports.getElectronModulePath = getElectronModulePath;
async function getElectronVersion(dir, packageJSON) {
    const packageName = getElectronModuleName(packageJSON);
    // Why: checked in getElectronModuleName
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    let version = packageJSON.devDependencies[packageName];
    if (!semver_1.default.valid(version)) {
        // It's not an exact version, find it in the actual module
        const electronPackageJSONPath = await getElectronPackageJSONPath(dir, packageName);
        if (electronPackageJSONPath) {
            const electronPackageJSON = await fs_extra_1.default.readJson(electronPackageJSONPath);
            version = electronPackageJSON.version;
        }
        else {
            throw new PackageNotFoundError(packageName, dir);
        }
    }
    return version;
}
exports.getElectronVersion = getElectronVersion;
function updateElectronDependency(packageJSON, dev, exact) {
    const alteredDev = [].concat(dev);
    let alteredExact = [].concat(exact);
    // Why: checked in getElectronModuleName
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    if (Object.keys(packageJSON.devDependencies).find(findElectronDep)) {
        alteredExact = alteredExact.filter((dep) => dep !== 'electron');
    }
    else if (packageJSON.dependencies) {
        const electronKey = Object.keys(packageJSON.dependencies).find(findElectronDep);
        if (electronKey) {
            alteredExact = alteredExact.filter((dep) => dep !== 'electron');
            d(`Moving ${electronKey} from dependencies to devDependencies`);
            alteredDev.push(`${electronKey}@${packageJSON.dependencies[electronKey]}`);
            delete packageJSON.dependencies[electronKey];
        }
    }
    return [alteredDev, alteredExact];
}
exports.updateElectronDependency = updateElectronDependency;
//# sourceMappingURL=data:application/json;base64,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