"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const rebuild_1 = require("@electron/rebuild");
if (!process.send) {
    console.error('The remote rebuilder expects to be spawned with an IPC channel');
    // eslint-disable-next-line no-process-exit
    process.exit(1);
}
const options = JSON.parse(process.argv[2]);
const rebuilder = (0, rebuild_1.rebuild)(options);
rebuilder.lifecycle.on('module-found', () => process.send?.({ msg: 'module-found' }));
rebuilder.lifecycle.on('module-done', () => process.send?.({ msg: 'module-done' }));
rebuilder
    .then(() => {
    process.send?.({ msg: 'rebuild-done' });
    // eslint-disable-next-line no-process-exit
    return process.exit(0);
})
    .catch((err) => {
    process.send?.({
        msg: 'rebuild-error',
        err: {
            message: err.message,
            stack: err.stack,
        },
    });
    // eslint-disable-next-line no-process-exit
    process.exit(0);
});
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicmVtb3RlLXJlYnVpbGQuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi9zcmMvcmVtb3RlLXJlYnVpbGQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7QUFBQSwrQ0FBNEQ7QUFFNUQsSUFBSSxDQUFDLE9BQU8sQ0FBQyxJQUFJLEVBQUUsQ0FBQztJQUNsQixPQUFPLENBQUMsS0FBSyxDQUFDLGdFQUFnRSxDQUFDLENBQUM7SUFDaEYsMkNBQTJDO0lBQzNDLE9BQU8sQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUM7QUFDbEIsQ0FBQztBQUVELE1BQU0sT0FBTyxHQUFtQixJQUFJLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztBQUU1RCxNQUFNLFNBQVMsR0FBRyxJQUFBLGlCQUFPLEVBQUMsT0FBTyxDQUFDLENBQUM7QUFFbkMsU0FBUyxDQUFDLFNBQVMsQ0FBQyxFQUFFLENBQUMsY0FBYyxFQUFFLEdBQUcsRUFBRSxDQUFDLE9BQU8sQ0FBQyxJQUFJLEVBQUUsQ0FBQyxFQUFFLEdBQUcsRUFBRSxjQUFjLEVBQUUsQ0FBQyxDQUFDLENBQUM7QUFDdEYsU0FBUyxDQUFDLFNBQVMsQ0FBQyxFQUFFLENBQUMsYUFBYSxFQUFFLEdBQUcsRUFBRSxDQUFDLE9BQU8sQ0FBQyxJQUFJLEVBQUUsQ0FBQyxFQUFFLEdBQUcsRUFBRSxhQUFhLEVBQUUsQ0FBQyxDQUFDLENBQUM7QUFFcEYsU0FBUztLQUNOLElBQUksQ0FBQyxHQUFHLEVBQUU7SUFDVCxPQUFPLENBQUMsSUFBSSxFQUFFLENBQUMsRUFBRSxHQUFHLEVBQUUsY0FBYyxFQUFFLENBQUMsQ0FBQztJQUN4QywyQ0FBMkM7SUFDM0MsT0FBTyxPQUFPLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDO0FBQ3pCLENBQUMsQ0FBQztLQUNELEtBQUssQ0FBQyxDQUFDLEdBQUcsRUFBRSxFQUFFO0lBQ2IsT0FBTyxDQUFDLElBQUksRUFBRSxDQUFDO1FBQ2IsR0FBRyxFQUFFLGVBQWU7UUFDcEIsR0FBRyxFQUFFO1lBQ0gsT0FBTyxFQUFFLEdBQUcsQ0FBQyxPQUFPO1lBQ3BCLEtBQUssRUFBRSxHQUFHLENBQUMsS0FBSztTQUNqQjtLQUNGLENBQUMsQ0FBQztJQUNILDJDQUEyQztJQUMzQyxPQUFPLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDO0FBQ2xCLENBQUMsQ0FBQyxDQUFDIn0=