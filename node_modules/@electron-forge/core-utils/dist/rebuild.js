"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.listrCompatibleRebuildHook = void 0;
const cp = __importStar(require("node:child_process"));
const path = __importStar(require("node:path"));
const listrCompatibleRebuildHook = async (buildPath, electronVersion, platform, arch, config = {}, task, taskTitlePrefix = '') => {
    task.title = `${taskTitlePrefix}Preparing native dependencies`;
    const options = {
        ...config,
        buildPath,
        electronVersion,
        arch,
    };
    const child = cp.fork(path.resolve(__dirname, 'remote-rebuild.js'), [JSON.stringify(options)], {
        stdio: ['pipe', 'pipe', 'pipe', 'ipc'],
    });
    let pendingError;
    let found = 0;
    let done = 0;
    const redraw = () => {
        task.title = `${taskTitlePrefix}Preparing native dependencies: ${done} / ${found}`;
    };
    child.stdout?.on('data', (chunk) => {
        task.output = chunk.toString();
    });
    child.stderr?.on('data', (chunk) => {
        task.output = chunk.toString();
    });
    child.on('message', (message) => {
        switch (message.msg) {
            case 'module-found': {
                found += 1;
                redraw();
                break;
            }
            case 'module-done': {
                done += 1;
                redraw();
                break;
            }
            case 'rebuild-error': {
                pendingError = new Error(message.err.message);
                pendingError.stack = message.err.stack;
                break;
            }
            case 'rebuild-done': {
                if (task.task.rendererTaskOptions && 'persistentOutput' in task.task.rendererTaskOptions) {
                    task.task.rendererTaskOptions.persistentOutput = false;
                }
                break;
            }
        }
    });
    await new Promise((resolve, reject) => {
        child.on('exit', (code) => {
            if (code === 0 && !pendingError) {
                resolve();
            }
            else {
                reject(pendingError || new Error(`Rebuilder failed with exit code: ${code}`));
            }
        });
    });
};
exports.listrCompatibleRebuildHook = listrCompatibleRebuildHook;
//# sourceMappingURL=data:application/json;base64,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