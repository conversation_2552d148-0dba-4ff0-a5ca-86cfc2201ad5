{"version": 3, "file": "forge-config.d.ts", "sourceRoot": "", "sources": ["../../src/util/forge-config.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,mBAAmB,EAAE,MAAM,8BAA8B,CAAC;AA8EhF,eAAO,MAAM,sBAAsB,EAAE,GAAG,CAAC,MAAM,EAAE,WAAW,CAAa,CAAC;AAC1E,wBAAgB,+BAA+B,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,GAAG,IAAI,CAEtF;AACD,wBAAgB,iCAAiC,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI,CAEnE;AAED,MAAM,MAAM,kBAAkB,CAAC,CAAC,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;AAClE,MAAM,MAAM,qBAAqB,CAAC,CAAC,IAAI;IACrC,GAAG,EAAE,kBAAkB,CAAC,CAAC,CAAC,CAAC;IAC3B,2BAA2B,EAAE,IAAI,CAAC;CACnC,CAAC;AAEF,wBAAgB,mBAAmB,CAAC,CAAC,EAAE,GAAG,EAAE,kBAAkB,CAAC,CAAC,CAAC,GAAG,qBAAqB,CAAC,CAAC,CAAC,CAK3F;AAED,wBAAsB,0BAA0B,CAAC,GAAG,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,GAAG,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,CAEjH;AAGD,wBAAgB,oBAAoB,CAAC,GAAG,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,IAAI,CAYlF;8BAK0B,MAAM,KAAG,QAAQ,mBAAmB,CAAC;AAAhE,wBAwDE"}