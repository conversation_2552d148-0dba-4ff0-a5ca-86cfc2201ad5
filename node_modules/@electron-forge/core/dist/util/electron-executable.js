"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const node_path_1 = __importDefault(require("node:path"));
const core_utils_1 = require("@electron-forge/core-utils");
const log_symbols_1 = __importDefault(require("log-symbols"));
async function locateElectronExecutable(dir, packageJSON) {
    const electronModulePath = await (0, core_utils_1.getElectronModulePath)(dir, packageJSON);
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    let electronExecPath = require(electronModulePath || node_path_1.default.resolve(dir, 'node_modules/electron'));
    if (typeof electronExecPath !== 'string') {
        console.warn(log_symbols_1.default.warning, 'Returned Electron executable path is not a string, defaulting to a hardcoded location. Value:', electronExecPath);
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        electronExecPath = require(node_path_1.default.resolve(dir, 'node_modules/electron'));
    }
    return electronExecPath;
}
exports.default = locateElectronExecutable;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZWxlY3Ryb24tZXhlY3V0YWJsZS5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uL3NyYy91dGlsL2VsZWN0cm9uLWV4ZWN1dGFibGUudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSwwREFBNkI7QUFFN0IsMkRBQW1FO0FBQ25FLDhEQUFxQztBQUl0QixLQUFLLFVBQVUsd0JBQXdCLENBQUMsR0FBVyxFQUFFLFdBQXdCO0lBQzFGLE1BQU0sa0JBQWtCLEdBQXVCLE1BQU0sSUFBQSxrQ0FBcUIsRUFBQyxHQUFHLEVBQUUsV0FBVyxDQUFDLENBQUM7SUFFN0YsaUVBQWlFO0lBQ2pFLElBQUksZ0JBQWdCLEdBQUcsT0FBTyxDQUFDLGtCQUFrQixJQUFJLG1CQUFJLENBQUMsT0FBTyxDQUFDLEdBQUcsRUFBRSx1QkFBdUIsQ0FBQyxDQUFDLENBQUM7SUFFakcsSUFBSSxPQUFPLGdCQUFnQixLQUFLLFFBQVEsRUFBRSxDQUFDO1FBQ3pDLE9BQU8sQ0FBQyxJQUFJLENBQUMscUJBQVUsQ0FBQyxPQUFPLEVBQUUsK0ZBQStGLEVBQUUsZ0JBQWdCLENBQUMsQ0FBQztRQUNwSixpRUFBaUU7UUFDakUsZ0JBQWdCLEdBQUcsT0FBTyxDQUFDLG1CQUFJLENBQUMsT0FBTyxDQUFDLEdBQUcsRUFBRSx1QkFBdUIsQ0FBQyxDQUFDLENBQUM7SUFDekUsQ0FBQztJQUVELE9BQU8sZ0JBQWdCLENBQUM7QUFDMUIsQ0FBQztBQWJELDJDQWFDIn0=