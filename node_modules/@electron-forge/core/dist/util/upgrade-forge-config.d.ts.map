{"version": 3, "file": "upgrade-forge-config.d.ts", "sourceRoot": "", "sources": ["../../src/util/upgrade-forge-config.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,aAAa,EAAoD,MAAM,8BAA8B,CAAC;AAM5H,KAAK,aAAa,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG;IAC7C,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,EAAE,MAAM,CAAC;IACd,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;CAClC,CAAC;AAEF,KAAK,YAAY,GAAG;IAClB,YAAY,CAAC,EAAE,MAAM,CAAC,aAAa,EAAE,MAAM,EAAE,CAAC,CAAC;IAC/C,sBAAsB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACjD,qBAAqB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAChD,wBAAwB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACnD,oBAAoB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAC/C,wBAAwB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACnD,uBAAuB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAClD,uBAAuB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAClD,qBAAqB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAChD,oBAAoB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAC/C,kBAAkB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAE7C,iBAAiB,CAAC,EAAE,aAAa,CAAC;IAClC,EAAE,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAC7B,yBAAyB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACpD,SAAS,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;CACrC,CAAC;AAIF,KAAK,gBAAgB,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG;IAChD,MAAM,EAAE;QACN,KAAK,EAAE,WAAW,CAAC;KACpB,CAAC;IACF,eAAe,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;CACzC,CAAC;AAuGF;;GAEG;AACH,MAAM,CAAC,OAAO,UAAU,kBAAkB,CAAC,YAAY,EAAE,YAAY,GAAG,WAAW,CAclF;AAED,wBAAgB,0BAA0B,CAAC,WAAW,EAAE,gBAAgB,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,MAAM,EAAE,CASrG"}