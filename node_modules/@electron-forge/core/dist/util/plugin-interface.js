"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const plugin_base_1 = require("@electron-forge/plugin-base");
const chalk_1 = __importDefault(require("chalk"));
const debug_1 = __importDefault(require("debug"));
const import_search_1 = __importDefault(require("./import-search"));
const d = (0, debug_1.default)('electron-forge:plugins');
function isForgePlugin(plugin) {
    return plugin.__isElectronForgePlugin;
}
class PluginInterface {
    static async create(dir, forgeConfig) {
        const int = new PluginInterface(dir, forgeConfig);
        await int._pluginPromise;
        return int;
    }
    constructor(dir, forgeConfig) {
        this.plugins = [];
        this._pluginPromise = Promise.resolve();
        this._pluginPromise = Promise.all(forgeConfig.plugins.map(async (plugin) => {
            if (isForgePlugin(plugin)) {
                return plugin;
            }
            if (typeof plugin === 'object' && 'name' in plugin && 'config' in plugin) {
                const { name: pluginName, config: opts } = plugin;
                if (typeof pluginName !== 'string') {
                    throw new Error(`Expected plugin[0] to be a string but found ${pluginName}`);
                }
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                const Plugin = await (0, import_search_1.default)(dir, [pluginName]);
                if (!Plugin) {
                    throw new Error(`Could not find module with name: ${pluginName}. Make sure it's listed in the devDependencies of your package.json`);
                }
                return new Plugin(opts);
            }
            throw new Error(`Expected plugin to either be a plugin instance or a { name, config } object but found ${JSON.stringify(plugin)}`);
        })).then((plugins) => {
            this.plugins = plugins;
            for (const plugin of this.plugins) {
                plugin.init(dir, forgeConfig);
            }
            return;
        });
        // TODO: fix hack
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        this.config = null;
        Object.defineProperty(this, 'config', {
            value: forgeConfig,
            enumerable: false,
            configurable: false,
            writable: false,
        });
        this.triggerHook = this.triggerHook.bind(this);
        this.overrideStartLogic = this.overrideStartLogic.bind(this);
    }
    async triggerHook(hookName, hookArgs) {
        for (const plugin of this.plugins) {
            if (typeof plugin.getHooks === 'function') {
                let hooks = plugin.getHooks()[hookName];
                if (hooks) {
                    if (typeof hooks === 'function')
                        hooks = [hooks];
                    for (const hook of hooks) {
                        await hook(this.config, ...hookArgs);
                    }
                }
            }
        }
    }
    async getHookListrTasks(childTrace, hookName, hookArgs) {
        const tasks = [];
        for (const plugin of this.plugins) {
            if (typeof plugin.getHooks === 'function') {
                let hooks = plugin.getHooks()[hookName];
                if (hooks) {
                    if (typeof hooks === 'function')
                        hooks = [hooks];
                    for (const hook of hooks) {
                        tasks.push({
                            title: `${chalk_1.default.cyan(`[plugin-${plugin.name}]`)} ${hook.__hookName || `Running ${chalk_1.default.yellow(hookName)} hook`}`,
                            task: childTrace({ name: 'forge-plugin-hook', category: '@electron-forge/hooks', extraDetails: { plugin: plugin.name, hook: hookName } }, async (_, __, task) => {
                                if (hook.__hookName) {
                                    // Also give it the task
                                    return await hook.call(task, this.config, ...hookArgs);
                                }
                                else {
                                    await hook(this.config, ...hookArgs);
                                }
                            }),
                            rendererOptions: {},
                        });
                    }
                }
            }
        }
        return tasks;
    }
    async triggerMutatingHook(hookName, ...item) {
        let result = item[0];
        for (const plugin of this.plugins) {
            if (typeof plugin.getHooks === 'function') {
                let hooks = plugin.getHooks()[hookName];
                if (hooks) {
                    if (typeof hooks === 'function')
                        hooks = [hooks];
                    for (const hook of hooks) {
                        result = (await hook(this.config, ...item)) || result;
                    }
                }
            }
        }
        return result;
    }
    async overrideStartLogic(opts) {
        let newStartFn;
        const claimed = [];
        for (const plugin of this.plugins) {
            if (typeof plugin.startLogic === 'function' && plugin.startLogic !== plugin_base_1.PluginBase.prototype.startLogic) {
                claimed.push(plugin.name);
                newStartFn = plugin.startLogic;
            }
        }
        if (claimed.length > 1) {
            throw new Error(`Multiple plugins tried to take control of the start command, please remove one of them\n --> ${claimed.join(', ')}`);
        }
        if (claimed.length === 1 && newStartFn) {
            d(`plugin: "${claimed[0]}" has taken control of the start command`);
            const result = await newStartFn(opts);
            if (typeof result === 'object' && 'tasks' in result) {
                result.tasks = result.tasks.map((task) => ({
                    ...task,
                    title: `${chalk_1.default.cyan(`[plugin-${claimed[0]}]`)} ${task.title}`,
                }));
            }
            return result;
        }
        return false;
    }
}
exports.default = PluginInterface;
//# sourceMappingURL=data:application/json;base64,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