"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const node_path_1 = __importDefault(require("node:path"));
const core_utils_1 = require("@electron-forge/core-utils");
const debug_1 = __importDefault(require("debug"));
const fs_extra_1 = __importDefault(require("fs-extra"));
const forge_config_1 = require("./forge-config");
const read_package_json_1 = require("./read-package-json");
const d = (0, debug_1.default)('electron-forge:project-resolver');
// FIXME: If we want getElectronVersion to be overridable by plugins
//        and / or forge config then we need to be able to resolve
//        the dir without calling getElectronVersion
exports.default = async (dir) => {
    let mDir = node_path_1.default.resolve(dir);
    let bestGuessDir = null;
    let lastError = null;
    let prevDir;
    while (prevDir !== mDir) {
        prevDir = mDir;
        d('searching for project in:', mDir);
        if (forge_config_1.registeredForgeConfigs.has(mDir)) {
            d('virtual config found in:', mDir);
            return mDir;
        }
        const testPath = node_path_1.default.resolve(mDir, 'package.json');
        if (await fs_extra_1.default.pathExists(testPath)) {
            const packageJSON = await (0, read_package_json_1.readRawPackageJson)(mDir);
            // TODO: Move this check to inside the forge config resolver and use
            //       mutatedPackageJson reader
            try {
                await (0, core_utils_1.getElectronVersion)(mDir, packageJSON);
            }
            catch (err) {
                if (err instanceof Error) {
                    lastError = err.message;
                }
            }
            if (packageJSON.config && packageJSON.config.forge) {
                d('electron-forge compatible package.json found in', testPath);
                return mDir;
            }
            if (packageJSON.devDependencies?.['@electron-forge/cli'] || packageJSON.devDependencies?.['@electron-forge/core']) {
                d('package.json with forge dependency found in', testPath);
                return mDir;
            }
            bestGuessDir = mDir;
        }
        mDir = node_path_1.default.dirname(mDir);
    }
    if (bestGuessDir) {
        d('guessing on the best electron-forge package.json found in', bestGuessDir);
        return bestGuessDir;
    }
    if (lastError) {
        throw new Error(lastError);
    }
    return null;
};
//# sourceMappingURL=data:application/json;base64,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