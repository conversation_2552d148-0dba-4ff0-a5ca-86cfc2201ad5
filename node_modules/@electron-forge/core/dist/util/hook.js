"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.runMutatingHook = exports.getHookListrTasks = exports.runHook = void 0;
const chalk_1 = __importDefault(require("chalk"));
const debug_1 = __importDefault(require("debug"));
const d = (0, debug_1.default)('electron-forge:hook');
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const runHook = async (forgeConfig, hookName, ...hookArgs) => {
    const { hooks } = forgeConfig;
    if (hooks) {
        d(`hook triggered: ${hookName}`);
        if (typeof hooks[hookName] === 'function') {
            d('calling hook:', hookName, 'with args:', hookArgs);
            await hooks[hookName](forgeConfig, ...hookArgs);
        }
    }
    await forgeConfig.pluginInterface.triggerHook(hookName, hookArgs);
};
exports.runHook = runHook;
const getHookListrTasks = async (childTrace, forgeConfig, hookName, ...hookArgs) => {
    const { hooks } = forgeConfig;
    const tasks = [];
    if (hooks) {
        d(`hook triggered: ${hookName}`);
        if (typeof hooks[hookName] === 'function') {
            d('calling hook:', hookName, 'with args:', hookArgs);
            tasks.push({
                title: `Running ${chalk_1.default.yellow(hookName)} hook from forgeConfig`,
                task: childTrace({ name: 'forge-config-hook', category: '@electron-forge/hooks', extraDetails: { hook: hookName } }, async () => {
                    await hooks[hookName](forgeConfig, ...hookArgs);
                }),
            });
        }
    }
    tasks.push(...(await forgeConfig.pluginInterface.getHookListrTasks(childTrace, hookName, hookArgs)));
    return tasks;
};
exports.getHookListrTasks = getHookListrTasks;
async function runMutatingHook(forgeConfig, hookName, ...item) {
    const { hooks } = forgeConfig;
    if (hooks) {
        d(`hook triggered: ${hookName}`);
        if (typeof hooks[hookName] === 'function') {
            d('calling mutating hook:', hookName, 'with item:', item[0]);
            const hook = hooks[hookName];
            const result = await hook(forgeConfig, ...item);
            if (typeof result !== 'undefined') {
                item[0] = result;
            }
        }
    }
    return forgeConfig.pluginInterface.triggerMutatingHook(hookName, item[0]);
}
exports.runMutatingHook = runMutatingHook;
//# sourceMappingURL=data:application/json;base64,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