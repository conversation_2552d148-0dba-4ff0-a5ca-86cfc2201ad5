"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateUpgradedForgeDevDeps = void 0;
const node_path_1 = __importDefault(require("node:path"));
const init_npm_1 = require("../api/init-scripts/init-npm");
function mapMakeTargets(forge5Config) {
    const makeTargets = new Map();
    if (forge5Config.make_targets) {
        for (const [platform, targets] of Object.entries(forge5Config.make_targets)) {
            for (const target of targets) {
                let platforms = makeTargets.get(target);
                if (platforms === undefined) {
                    platforms = [];
                    makeTargets.set(target, platforms);
                }
                platforms.push(platform);
            }
        }
    }
    return makeTargets;
}
const forge5MakerMappings = new Map([
    ['electronInstallerDebian', 'deb'],
    ['electronInstallerDMG', 'dmg'],
    ['electronInstallerFlatpak', 'flatpak'],
    ['electronInstallerRedhat', 'rpm'],
    ['electronInstallerSnap', 'snap'],
    ['electronWinstallerConfig', 'squirrel'],
    ['electronWixMSIConfig', 'wix'],
    ['windowsStoreConfig', 'appx'],
]);
/**
 * Converts Forge v5 maker config to v6.
 */
function generateForgeMakerConfig(forge5Config) {
    const makeTargets = mapMakeTargets(forge5Config);
    const makers = [];
    for (const [forge5Key, makerType] of forge5MakerMappings) {
        const config = forge5Config[forge5Key];
        if (config) {
            makers.push({
                name: `@electron-forge/maker-${makerType}`,
                config: forge5Config[forge5Key],
                platforms: makeTargets.get(makerType) || [],
            });
        }
    }
    const zipPlatforms = makeTargets.get('zip');
    if (zipPlatforms) {
        makers.push({
            name: '@electron-forge/maker-zip',
            platforms: zipPlatforms,
        });
    }
    return makers;
}
const forge5PublisherMappings = new Map([
    ['github_repository', 'github'],
    ['s3', 's3'],
    ['electron-release-server', 'electron-release-server'],
    ['snapStore', 'snapcraft'],
]);
/**
 * Transforms v5 GitHub publisher config to v6 syntax.
 */
function transformGitHubPublisherConfig(config) {
    const { name, owner, options, ...gitHubConfig } = config;
    gitHubConfig.repository = { name, owner };
    if (options) {
        gitHubConfig.octokitOptions = options;
    }
    return gitHubConfig;
}
/**
 * Converts Forge v5 publisher config to v6.
 */
function generateForgePublisherConfig(forge5Config) {
    const publishers = [];
    for (const [forge5Key, publisherType] of forge5PublisherMappings) {
        let config = forge5Config[forge5Key];
        if (config) {
            if (publisherType === 'github') {
                config = transformGitHubPublisherConfig(config);
            }
            publishers.push({
                config,
                name: `@electron-forge/publisher-${publisherType}`,
                platforms: null,
            });
        }
    }
    return publishers;
}
/**
 * Upgrades Forge v5 config to v6.
 */
function upgradeForgeConfig(forge5Config) {
    const forgeConfig = {};
    if (forge5Config.electronPackagerConfig) {
        delete forge5Config.electronPackagerConfig.packageManager;
        forgeConfig.packagerConfig = forge5Config.electronPackagerConfig;
    }
    if (forge5Config.electronRebuildConfig) {
        forgeConfig.rebuildConfig = forge5Config.electronRebuildConfig;
    }
    forgeConfig.makers = generateForgeMakerConfig(forge5Config);
    forgeConfig.publishers = generateForgePublisherConfig(forge5Config);
    return forgeConfig;
}
exports.default = upgradeForgeConfig;
function updateUpgradedForgeDevDeps(packageJSON, devDeps) {
    const forgeConfig = packageJSON.config.forge;
    devDeps = devDeps.filter((dep) => !dep.startsWith('@electron-forge/maker-'));
    devDeps = devDeps.concat(forgeConfig.makers.map((maker) => (0, init_npm_1.siblingDep)(node_path_1.default.basename(maker.name))));
    devDeps = devDeps.concat(forgeConfig.publishers.map((publisher) => (0, init_npm_1.siblingDep)(node_path_1.default.basename(publisher.name))));
    return devDeps;
}
exports.updateUpgradedForgeDevDeps = updateUpgradedForgeDevDeps;
//# sourceMappingURL=data:application/json;base64,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