"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.renderConfigTemplate = exports.forgeConfigIsValidFilePath = exports.fromBuildIdentifier = exports.unregisterForgeConfigForDirectory = exports.registerForgeConfigForDirectory = exports.registeredForgeConfigs = void 0;
const node_path_1 = __importDefault(require("node:path"));
const fs_extra_1 = __importDefault(require("fs-extra"));
const interpret = __importStar(require("interpret"));
const lodash_1 = require("lodash");
const rechoir = __importStar(require("rechoir"));
// eslint-disable-next-line n/no-missing-import
const dynamic_import_js_1 = require("../../helper/dynamic-import.js");
const hook_1 = require("./hook");
const plugin_interface_1 = __importDefault(require("./plugin-interface"));
const read_package_json_1 = require("./read-package-json");
const underscoreCase = (str) => str
    .replace(/(.)([A-Z][a-z]+)/g, '$1_$2')
    .replace(/([a-z0-9])([A-Z])/g, '$1_$2')
    .toUpperCase();
/* eslint-disable @typescript-eslint/no-explicit-any */
function isBuildIdentifierConfig(value) {
    return value && typeof value === 'object' && value.__isMagicBuildIdentifierMap;
}
const proxify = (buildIdentifier, proxifiedObject, envPrefix) => {
    let newObject = {};
    if (Array.isArray(proxifiedObject)) {
        newObject = [];
    }
    for (const [key, val] of Object.entries(proxifiedObject)) {
        if (typeof val === 'object' && (val.constructor === Object || val.constructor === Array) && key !== 'pluginInterface' && !(val instanceof RegExp)) {
            newObject[key] = proxify(buildIdentifier, proxifiedObject[key], `${envPrefix}_${underscoreCase(key)}`);
        }
        else {
            newObject[key] = proxifiedObject[key];
        }
    }
    return new Proxy(newObject, {
        get(target, name, receiver) {
            // eslint-disable-next-line no-prototype-builtins
            if (!target.hasOwnProperty(name) && typeof name === 'string') {
                const envValue = process.env[`${envPrefix}_${underscoreCase(name)}`];
                if (envValue)
                    return envValue;
            }
            const value = Reflect.get(target, name, receiver);
            if (isBuildIdentifierConfig(value)) {
                const identifier = typeof buildIdentifier === 'function' ? buildIdentifier() : buildIdentifier;
                return value.map[identifier];
            }
            return value;
        },
        getOwnPropertyDescriptor(target, name) {
            const envValue = process.env[`${envPrefix}_${underscoreCase(name)}`];
            // eslint-disable-next-line no-prototype-builtins
            if (target.hasOwnProperty(name)) {
                return Reflect.getOwnPropertyDescriptor(target, name);
            }
            if (envValue) {
                return {
                    writable: true,
                    enumerable: true,
                    configurable: true,
                    value: envValue,
                };
            }
            return undefined;
        },
    });
};
/* eslint-enable @typescript-eslint/no-explicit-any */
exports.registeredForgeConfigs = new Map();
function registerForgeConfigForDirectory(dir, config) {
    exports.registeredForgeConfigs.set(node_path_1.default.resolve(dir), config);
}
exports.registerForgeConfigForDirectory = registerForgeConfigForDirectory;
function unregisterForgeConfigForDirectory(dir) {
    exports.registeredForgeConfigs.delete(node_path_1.default.resolve(dir));
}
exports.unregisterForgeConfigForDirectory = unregisterForgeConfigForDirectory;
function fromBuildIdentifier(map) {
    return {
        map,
        __isMagicBuildIdentifierMap: true,
    };
}
exports.fromBuildIdentifier = fromBuildIdentifier;
async function forgeConfigIsValidFilePath(dir, forgeConfig) {
    return typeof forgeConfig === 'string' && ((await fs_extra_1.default.pathExists(node_path_1.default.resolve(dir, forgeConfig))) || fs_extra_1.default.pathExists(node_path_1.default.resolve(dir, `${forgeConfig}.js`)));
}
exports.forgeConfigIsValidFilePath = forgeConfigIsValidFilePath;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function renderConfigTemplate(dir, templateObj, obj) {
    for (const [key, value] of Object.entries(obj)) {
        if (typeof value === 'object' && value !== null) {
            renderConfigTemplate(dir, templateObj, value);
        }
        else if (typeof value === 'string') {
            obj[key] = (0, lodash_1.template)(value)(templateObj);
            if (obj[key].startsWith('require:')) {
                // eslint-disable-next-line @typescript-eslint/no-require-imports
                obj[key] = require(node_path_1.default.resolve(dir, obj[key].substr(8)));
            }
        }
    }
}
exports.renderConfigTemplate = renderConfigTemplate;
exports.default = async (dir) => {
    let forgeConfig = exports.registeredForgeConfigs.get(dir);
    const packageJSON = await (0, read_package_json_1.readRawPackageJson)(dir);
    if (forgeConfig === undefined) {
        forgeConfig = packageJSON.config && packageJSON.config.forge ? packageJSON.config.forge : null;
    }
    if (!forgeConfig || typeof forgeConfig === 'string') {
        for (const extension of ['.js', ...Object.keys(interpret.extensions)]) {
            const pathToConfig = node_path_1.default.resolve(dir, `forge.config${extension}`);
            if (await fs_extra_1.default.pathExists(pathToConfig)) {
                rechoir.prepare(interpret.extensions, pathToConfig, dir);
                forgeConfig = `forge.config${extension}`;
                break;
            }
        }
    }
    forgeConfig = forgeConfig || {};
    if (await forgeConfigIsValidFilePath(dir, forgeConfig)) {
        const forgeConfigPath = node_path_1.default.resolve(dir, forgeConfig);
        try {
            // The loaded "config" could potentially be a static forge config, ESM module or async function
            const loaded = (await (0, dynamic_import_js_1.dynamicImportMaybe)(forgeConfigPath));
            const maybeForgeConfig = 'default' in loaded ? loaded.default : loaded;
            forgeConfig = typeof maybeForgeConfig === 'function' ? await maybeForgeConfig() : maybeForgeConfig;
        }
        catch (err) {
            console.error(`Failed to load: ${forgeConfigPath}`);
            throw err;
        }
    }
    else if (typeof forgeConfig !== 'object') {
        throw new Error('Expected packageJSON.config.forge to be an object or point to a requirable JS file');
    }
    const defaultForgeConfig = {
        rebuildConfig: {},
        packagerConfig: {},
        makers: [],
        publishers: [],
        plugins: [],
    };
    let resolvedForgeConfig = {
        ...defaultForgeConfig,
        ...forgeConfig,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        pluginInterface: null,
    };
    const templateObj = { ...packageJSON, year: new Date().getFullYear() };
    renderConfigTemplate(dir, templateObj, resolvedForgeConfig);
    resolvedForgeConfig.pluginInterface = await plugin_interface_1.default.create(dir, resolvedForgeConfig);
    resolvedForgeConfig = await (0, hook_1.runMutatingHook)(resolvedForgeConfig, 'resolveForgeConfig', resolvedForgeConfig);
    return proxify(resolvedForgeConfig.buildIdentifier || '', resolvedForgeConfig, 'ELECTRON_FORGE');
};
//# sourceMappingURL=data:application/json;base64,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