"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const node_child_process_1 = require("node:child_process");
const core_utils_1 = require("@electron-forge/core-utils");
const tracer_1 = require("@electron-forge/tracer");
const chalk_1 = __importDefault(require("chalk"));
const debug_1 = __importDefault(require("debug"));
const listr2_1 = require("listr2");
const electron_executable_1 = __importDefault(require("../util/electron-executable"));
const forge_config_1 = __importDefault(require("../util/forge-config"));
const hook_1 = require("../util/hook");
const read_package_json_1 = require("../util/read-package-json");
const resolve_dir_1 = __importDefault(require("../util/resolve-dir"));
const d = (0, debug_1.default)('electron-forge:start');
exports.default = (0, tracer_1.autoTrace)({ name: 'start()', category: '@electron-forge/core' }, async (childTrace, { dir: providedDir = process.cwd(), appPath = '.', interactive = false, enableLogging = false, args = [], runAsNode = false, inspect = false, inspectBrk = false, }) => {
    const platform = process.env.npm_config_platform || process.platform;
    const arch = process.env.npm_config_arch || process.arch;
    const listrOptions = {
        concurrent: false,
        rendererOptions: {
            collapseErrors: false,
        },
        silentRendererCondition: !interactive,
        fallbackRendererCondition: Boolean(process.env.DEBUG) || Boolean(process.env.CI),
    };
    const runner = new listr2_1.Listr([
        {
            title: 'Locating application',
            task: childTrace({ name: 'locate-application', category: '@electron-forge/core' }, async (_, ctx) => {
                const resolvedDir = await (0, resolve_dir_1.default)(providedDir);
                if (!resolvedDir) {
                    throw new Error('Failed to locate startable Electron application');
                }
                ctx.dir = resolvedDir;
            }),
        },
        {
            title: 'Loading configuration',
            task: childTrace({ name: 'load-forge-config', category: '@electron-forge/core' }, async (_, ctx) => {
                const { dir } = ctx;
                ctx.forgeConfig = await (0, forge_config_1.default)(dir);
                ctx.packageJSON = await (0, read_package_json_1.readMutatedPackageJson)(dir, ctx.forgeConfig);
                if (!ctx.packageJSON.version) {
                    throw new Error(`Please set your application's 'version' in '${dir}/package.json'.`);
                }
            }),
        },
        {
            title: 'Preparing native dependencies',
            task: childTrace({ name: 'prepare-native-dependencies', category: '@electron-forge/core' }, async (_, { dir, forgeConfig, packageJSON }, task) => {
                await (0, core_utils_1.listrCompatibleRebuildHook)(dir, await (0, core_utils_1.getElectronVersion)(dir, packageJSON), platform, arch, forgeConfig.rebuildConfig, task);
            }),
            rendererOptions: {
                persistentOutput: true,
                bottomBar: Infinity,
                timer: { ...listr2_1.PRESET_TIMER },
            },
        },
        {
            title: `Running ${chalk_1.default.yellow('generateAssets')} hook`,
            task: childTrace({ name: 'run-generateAssets-hook', category: '@electron-forge/core' }, async (childTrace, { forgeConfig }, task) => {
                return (0, tracer_1.delayTraceTillSignal)(childTrace, task.newListr(await (0, hook_1.getHookListrTasks)(childTrace, forgeConfig, 'generateAssets', platform, arch)), 'run');
            }),
        },
        {
            title: `Running ${chalk_1.default.yellow('preStart')} hook`,
            task: childTrace({ name: 'run-preStart-hook', category: '@electron-forge/core' }, async (childTrace, { forgeConfig }, task) => {
                return (0, tracer_1.delayTraceTillSignal)(childTrace, task.newListr(await (0, hook_1.getHookListrTasks)(childTrace, forgeConfig, 'preStart')), 'run');
            }),
        },
    ], listrOptions);
    await runner.run();
    const { dir, forgeConfig, packageJSON } = runner.ctx;
    let lastSpawned = null;
    const forgeSpawn = async () => {
        let electronExecPath = null;
        // If a plugin has taken over the start command let's stop here
        let spawnedPluginChild = await forgeConfig.pluginInterface.overrideStartLogic({
            dir,
            appPath,
            interactive,
            enableLogging,
            args,
            runAsNode,
            inspect,
            inspectBrk,
        });
        if (typeof spawnedPluginChild === 'object' && 'tasks' in spawnedPluginChild) {
            const innerRunner = new listr2_1.Listr([], listrOptions);
            for (const task of spawnedPluginChild.tasks) {
                innerRunner.add(task);
            }
            await innerRunner.run();
            spawnedPluginChild = spawnedPluginChild.result;
        }
        let prefixArgs = [];
        if (typeof spawnedPluginChild === 'string') {
            electronExecPath = spawnedPluginChild;
        }
        else if (Array.isArray(spawnedPluginChild)) {
            [electronExecPath, ...prefixArgs] = spawnedPluginChild;
        }
        else if (spawnedPluginChild) {
            await (0, hook_1.runHook)(forgeConfig, 'postStart', spawnedPluginChild);
            return spawnedPluginChild;
        }
        if (!electronExecPath) {
            electronExecPath = await (0, electron_executable_1.default)(dir, packageJSON);
        }
        d('Electron binary path:', electronExecPath);
        const spawnOpts = {
            cwd: dir,
            stdio: 'inherit',
            env: {
                ...process.env,
                ...(enableLogging
                    ? {
                        ELECTRON_ENABLE_LOGGING: 'true',
                        ELECTRON_ENABLE_STACK_DUMPING: 'true',
                    }
                    : {}),
            },
        };
        if (runAsNode) {
            spawnOpts.env.ELECTRON_RUN_AS_NODE = 'true';
        }
        else {
            delete spawnOpts.env.ELECTRON_RUN_AS_NODE;
        }
        if (inspect) {
            args = ['--inspect'].concat(args);
        }
        if (inspectBrk) {
            args = ['--inspect-brk'].concat(args);
        }
        const spawned = (0, node_child_process_1.spawn)(electronExecPath, // eslint-disable-line @typescript-eslint/no-non-null-assertion
        prefixArgs.concat([appPath]).concat(args), spawnOpts);
        await (0, hook_1.runHook)(forgeConfig, 'postStart', spawned);
        return spawned;
    };
    const forgeSpawnWrapper = async () => {
        const spawned = await forgeSpawn();
        // When the child app is closed we should stop listening for stdin
        if (spawned) {
            if (interactive && process.stdin.isPaused()) {
                process.stdin.resume();
            }
            spawned.on('exit', () => {
                if (spawned.restarted) {
                    return;
                }
                if (interactive && !process.stdin.isPaused()) {
                    process.stdin.pause();
                }
            });
        }
        else if (interactive && !process.stdin.isPaused()) {
            process.stdin.pause();
        }
        lastSpawned = spawned;
        return lastSpawned;
    };
    if (interactive) {
        process.stdin.on('data', async (data) => {
            if (data.toString().trim() === 'rs' && lastSpawned) {
                console.info(chalk_1.default.cyan('\nRestarting App\n'));
                lastSpawned.restarted = true;
                lastSpawned.kill('SIGTERM');
                lastSpawned.emit('restarted', await forgeSpawnWrapper());
            }
        });
        process.stdin.resume();
    }
    const spawned = await forgeSpawnWrapper();
    if (interactive)
        console.log('');
    return spawned;
});
//# sourceMappingURL=data:application/json;base64,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