"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const node_path_1 = __importDefault(require("node:path"));
const tracer_1 = require("@electron-forge/tracer");
const chalk_1 = __importDefault(require("chalk"));
const debug_1 = __importDefault(require("debug"));
const fs_extra_1 = __importDefault(require("fs-extra"));
const listr2_1 = require("listr2");
const forge_config_1 = __importDefault(require("../util/forge-config"));
const import_search_1 = __importDefault(require("../util/import-search"));
const out_dir_1 = __importDefault(require("../util/out-dir"));
const publish_state_1 = __importDefault(require("../util/publish-state"));
const resolve_dir_1 = __importDefault(require("../util/resolve-dir"));
const make_1 = require("./make");
const d = (0, debug_1.default)('electron-forge:publish');
exports.default = (0, tracer_1.autoTrace)({ name: 'publish()', category: '@electron-forge/core' }, async (childTrace, { dir: providedDir = process.cwd(), interactive = false, makeOptions = {}, publishTargets = undefined, dryRun = false, dryRunResume = false, outDir, }) => {
    if (dryRun && dryRunResume) {
        throw new Error("Can't dry run and resume a dry run at the same time");
    }
    const listrOptions = {
        concurrent: false,
        rendererOptions: {
            collapseErrors: false,
        },
        silentRendererCondition: !interactive,
        fallbackRendererCondition: Boolean(process.env.DEBUG) || Boolean(process.env.CI),
    };
    const publishDistributablesTasks = (childTrace) => [
        {
            title: 'Publishing distributables',
            task: childTrace({ name: 'publish-distributables', category: '@electron-forge/core' }, async (childTrace, { dir, forgeConfig, makeResults, publishers }, task) => {
                if (publishers.length === 0) {
                    task.output = 'No publishers configured';
                    task.skip();
                    return;
                }
                return (0, tracer_1.delayTraceTillSignal)(childTrace, task.newListr(publishers.map((publisher) => ({
                    title: `${chalk_1.default.cyan(`[publisher-${publisher.name}]`)} Running the ${chalk_1.default.yellow('publish')} command`,
                    task: childTrace({ name: `publish-${publisher.name}`, category: '@electron-forge/core' }, async (childTrace, _, task) => {
                        const setStatusLine = (s) => {
                            task.output = s;
                        };
                        await publisher.publish({
                            dir,
                            makeResults: makeResults,
                            forgeConfig,
                            setStatusLine,
                        });
                    }),
                    rendererOptions: {
                        persistentOutput: true,
                    },
                })), {
                    rendererOptions: {
                        collapseSubtasks: false,
                        collapseErrors: false,
                    },
                }), 'run');
            }),
            rendererOptions: {
                persistentOutput: true,
            },
        },
    ];
    const runner = new listr2_1.Listr([
        {
            title: 'Loading configuration',
            task: childTrace({ name: 'load-forge-config', category: '@electron-forge/core' }, async (childTrace, ctx) => {
                const resolvedDir = await (0, resolve_dir_1.default)(providedDir);
                if (!resolvedDir) {
                    throw new Error('Failed to locate publishable Electron application');
                }
                ctx.dir = resolvedDir;
                ctx.forgeConfig = await (0, forge_config_1.default)(resolvedDir);
            }),
        },
        {
            title: 'Resolving publish targets',
            task: childTrace({ name: 'resolve-publish-targets', category: '@electron-forge/core' }, async (childTrace, ctx, task) => {
                const { dir, forgeConfig } = ctx;
                if (!publishTargets) {
                    publishTargets = forgeConfig.publishers || [];
                }
                publishTargets = publishTargets.map((target) => {
                    if (typeof target === 'string') {
                        return ((forgeConfig.publishers || []).find((p) => {
                            if (typeof p === 'string')
                                return false;
                            if (p.__isElectronForgePublisher)
                                return false;
                            return p.name === target;
                        }) || { name: target });
                    }
                    return target;
                });
                ctx.publishers = [];
                for (const publishTarget of publishTargets) {
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    let publisher;
                    if (publishTarget.__isElectronForgePublisher) {
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                        publisher = publishTarget;
                    }
                    else {
                        const resolvablePublishTarget = publishTarget;
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                        const PublisherClass = await (0, import_search_1.default)(dir, [resolvablePublishTarget.name]);
                        if (!PublisherClass) {
                            throw new Error(`Could not find a publish target with the name: ${resolvablePublishTarget.name}. Make sure it's listed in the devDependencies of your package.json`);
                        }
                        publisher = new PublisherClass(resolvablePublishTarget.config || {}, resolvablePublishTarget.platforms);
                    }
                    ctx.publishers.push(publisher);
                }
                if (ctx.publishers.length) {
                    task.output = `Publishing to the following targets: ${chalk_1.default.magenta(`${ctx.publishers.map((publisher) => publisher.name).join(', ')}`)}`;
                }
            }),
            rendererOptions: {
                persistentOutput: true,
            },
        },
        {
            title: dryRunResume ? 'Resuming from dry run...' : `Running ${chalk_1.default.yellow('make')} command`,
            task: childTrace({ name: dryRunResume ? 'resume-dry-run' : 'make()', category: '@electron-forge/core' }, async (childTrace, ctx, task) => {
                const { dir, forgeConfig } = ctx;
                const calculatedOutDir = outDir || (0, out_dir_1.default)(dir, forgeConfig);
                const dryRunDir = node_path_1.default.resolve(calculatedOutDir, 'publish-dry-run');
                if (dryRunResume) {
                    d('attempting to resume from dry run');
                    const publishes = await publish_state_1.default.loadFromDirectory(dryRunDir, dir);
                    task.title = `Resuming ${publishes.length} found dry runs...`;
                    return (0, tracer_1.delayTraceTillSignal)(childTrace, task.newListr(publishes.map((publishStates, index) => {
                        return {
                            title: `Publishing dry-run ${chalk_1.default.blue(`#${index + 1}`)}`,
                            task: childTrace({ name: `publish-dry-run-${index + 1}`, category: '@electron-forge/core' }, async (childTrace, ctx, task) => {
                                const restoredMakeResults = publishStates.map(({ state }) => state);
                                d('restoring publish settings from dry run');
                                for (const makeResult of restoredMakeResults) {
                                    makeResult.artifacts = await Promise.all(makeResult.artifacts.map(async (makePath) => {
                                        // standardize the path to artifacts across platforms
                                        const normalizedPath = makePath.split(/\/|\\/).join(node_path_1.default.sep);
                                        if (!(await fs_extra_1.default.pathExists(normalizedPath))) {
                                            throw new Error(`Attempted to resume a dry run, but an artifact (${normalizedPath}) could not be found`);
                                        }
                                        return normalizedPath;
                                    }));
                                }
                                d('publishing for given state set');
                                return (0, tracer_1.delayTraceTillSignal)(childTrace, task.newListr(publishDistributablesTasks(childTrace), {
                                    ctx: {
                                        ...ctx,
                                        makeResults: restoredMakeResults,
                                    },
                                    rendererOptions: {
                                        collapseSubtasks: false,
                                        collapseErrors: false,
                                    },
                                }), 'run');
                            }),
                        };
                    }), {
                        rendererOptions: {
                            collapseSubtasks: false,
                            collapseErrors: false,
                        },
                    }), 'run');
                }
                d('triggering make');
                return (0, tracer_1.delayTraceTillSignal)(childTrace, (0, make_1.listrMake)(childTrace, {
                    dir,
                    interactive,
                    ...makeOptions,
                }, (results) => {
                    ctx.makeResults = results;
                }), 'run');
            }),
        },
        ...(dryRunResume
            ? []
            : dryRun
                ? [
                    {
                        title: 'Saving dry-run state',
                        task: childTrace({ name: 'save-dry-run', category: '@electron-forge/core' }, async (childTrace, { dir, forgeConfig, makeResults }) => {
                            d('saving results of make in dry run state', makeResults);
                            const calculatedOutDir = outDir || (0, out_dir_1.default)(dir, forgeConfig);
                            const dryRunDir = node_path_1.default.resolve(calculatedOutDir, 'publish-dry-run');
                            await fs_extra_1.default.remove(dryRunDir);
                            await publish_state_1.default.saveToDirectory(dryRunDir, makeResults, dir);
                        }),
                    },
                ]
                : publishDistributablesTasks(childTrace)),
    ], listrOptions);
    await runner.run();
});
//# sourceMappingURL=data:application/json;base64,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