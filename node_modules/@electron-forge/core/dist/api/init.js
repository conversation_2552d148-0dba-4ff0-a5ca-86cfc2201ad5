"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const node_path_1 = __importDefault(require("node:path"));
const core_utils_1 = require("@electron-forge/core-utils");
const chalk_1 = __importDefault(require("chalk"));
const debug_1 = __importDefault(require("debug"));
const listr2_1 = require("listr2");
const semver_1 = __importDefault(require("semver"));
const install_dependencies_1 = __importStar(require("../util/install-dependencies"));
const read_package_json_1 = require("../util/read-package-json");
const find_template_1 = require("./init-scripts/find-template");
const init_directory_1 = require("./init-scripts/init-directory");
const init_git_1 = require("./init-scripts/init-git");
const init_link_1 = require("./init-scripts/init-link");
const init_npm_1 = require("./init-scripts/init-npm");
const d = (0, debug_1.default)('electron-forge:init');
async function validateTemplate(template, templateModule) {
    if (!templateModule.requiredForgeVersion) {
        throw new Error(`Cannot use a template (${template}) with this version of Electron Forge, as it does not specify its required Forge version.`);
    }
    const forgeVersion = (await (0, read_package_json_1.readRawPackageJson)(node_path_1.default.join(__dirname, '..', '..'))).version;
    if (!semver_1.default.satisfies(forgeVersion, templateModule.requiredForgeVersion)) {
        throw new Error(`Template (${template}) is not compatible with this version of Electron Forge (${forgeVersion}), it requires ${templateModule.requiredForgeVersion}`);
    }
}
exports.default = async ({ dir = process.cwd(), interactive = false, copyCIFiles = false, force = false, template = 'base' }) => {
    d(`Initializing in: ${dir}`);
    const runner = new listr2_1.Listr([
        {
            title: `Resolving package manager`,
            task: async (ctx, task) => {
                ctx.pm = await (0, core_utils_1.resolvePackageManager)();
                task.title = `Resolving package manager: ${chalk_1.default.cyan(ctx.pm.executable)}`;
            },
        },
        {
            title: `Locating custom template: "${template}"`,
            task: async (ctx) => {
                ctx.templateModule = await (0, find_template_1.findTemplate)(template);
            },
        },
        {
            title: 'Initializing directory',
            task: async (_, task) => {
                await (0, init_directory_1.initDirectory)(dir, task, force);
                await (0, init_git_1.initGit)(dir);
            },
            rendererOptions: { persistentOutput: true },
        },
        {
            title: 'Preparing template',
            task: async ({ templateModule }) => {
                await validateTemplate(template, templateModule);
            },
        },
        {
            title: `Initializing template`,
            task: async ({ templateModule }, task) => {
                if (typeof templateModule.initializeTemplate === 'function') {
                    const tasks = await templateModule.initializeTemplate(dir, { copyCIFiles, force });
                    if (tasks) {
                        return task.newListr(tasks, { concurrent: false });
                    }
                }
            },
        },
        {
            title: 'Installing template dependencies',
            task: async ({ templateModule }, task) => {
                return task.newListr([
                    {
                        title: 'Installing production dependencies',
                        task: async ({ pm }, task) => {
                            d('installing dependencies');
                            if (templateModule.dependencies?.length) {
                                task.output = `${pm.executable} ${pm.install} ${pm.dev} ${templateModule.dependencies.join(' ')}`;
                            }
                            return await (0, install_dependencies_1.default)(pm, dir, templateModule.dependencies || [], install_dependencies_1.DepType.PROD, install_dependencies_1.DepVersionRestriction.RANGE);
                        },
                        exitOnError: false,
                    },
                    {
                        title: 'Installing development dependencies',
                        task: async ({ pm }, task) => {
                            d('installing devDependencies');
                            if (templateModule.devDependencies?.length) {
                                task.output = `${pm.executable} ${pm.install} ${pm.dev} ${templateModule.devDependencies.join(' ')}`;
                            }
                            await (0, install_dependencies_1.default)(pm, dir, templateModule.devDependencies || [], install_dependencies_1.DepType.DEV);
                        },
                        exitOnError: false,
                    },
                    {
                        title: 'Finalizing dependencies',
                        task: async (_, task) => {
                            return task.newListr([
                                {
                                    title: 'Installing common dependencies',
                                    task: async ({ pm }, task) => {
                                        await (0, init_npm_1.initNPM)(pm, dir, task);
                                    },
                                    exitOnError: false,
                                },
                                {
                                    title: 'Linking Forge dependencies to local build',
                                    enabled: !!process.env.LINK_FORGE_DEPENDENCIES_ON_INIT,
                                    task: async ({ pm }, task) => {
                                        await (0, init_link_1.initLink)(pm, dir, task);
                                    },
                                    exitOnError: true,
                                },
                            ]);
                        },
                    },
                ], {
                    concurrent: false,
                });
            },
        },
    ], {
        concurrent: false,
        silentRendererCondition: !interactive,
        fallbackRendererCondition: Boolean(process.env.DEBUG) || Boolean(process.env.CI),
    });
    await runner.run();
};
//# sourceMappingURL=data:application/json;base64,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