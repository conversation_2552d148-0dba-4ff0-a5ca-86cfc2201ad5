"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const node_path_1 = __importDefault(require("node:path"));
const core_utils_1 = require("@electron-forge/core-utils");
const template_base_1 = __importDefault(require("@electron-forge/template-base"));
const tracer_1 = require("@electron-forge/tracer");
const chalk_1 = __importDefault(require("chalk"));
const debug_1 = __importDefault(require("debug"));
const fs_extra_1 = __importDefault(require("fs-extra"));
const listr2_1 = require("listr2");
const lodash_1 = require("lodash");
const install_dependencies_1 = __importStar(require("../util/install-dependencies"));
const read_package_json_1 = require("../util/read-package-json");
const upgrade_forge_config_1 = __importStar(require("../util/upgrade-forge-config"));
const init_git_1 = require("./init-scripts/init-git");
const init_npm_1 = require("./init-scripts/init-npm");
const d = (0, debug_1.default)('electron-forge:import');
exports.default = (0, tracer_1.autoTrace)({ name: 'import()', category: '@electron-forge/core' }, async (childTrace, { dir = process.cwd(), interactive = false, confirmImport, shouldContinueOnExisting, shouldRemoveDependency, shouldUpdateScript, outDir }) => {
    const listrOptions = {
        concurrent: false,
        rendererOptions: {
            collapseSubtasks: false,
            collapseErrors: false,
        },
        silentRendererCondition: !interactive,
        fallbackRendererCondition: Boolean(process.env.DEBUG) || Boolean(process.env.CI),
    };
    const runner = new listr2_1.Listr([
        {
            title: 'Locating importable project',
            task: childTrace({ name: 'locate-project', category: '@electron-forge/core' }, async () => {
                d(`Attempting to import project in: ${dir}`);
                if (!(await fs_extra_1.default.pathExists(dir)) || !(await fs_extra_1.default.pathExists(node_path_1.default.resolve(dir, 'package.json')))) {
                    throw new Error(`We couldn't find a project with a package.json file in: ${dir}`);
                }
                if (typeof confirmImport === 'function') {
                    if (!(await confirmImport())) {
                        // TODO: figure out if we can just return early here
                        // eslint-disable-next-line no-process-exit
                        process.exit(0);
                    }
                }
                await (0, init_git_1.initGit)(dir);
            }),
        },
        {
            title: 'Processing configuration and dependencies',
            rendererOptions: {
                persistentOutput: true,
                bottomBar: Infinity,
            },
            task: childTrace({ name: 'string', category: 'foo' }, async (_, ctx, task) => {
                const calculatedOutDir = outDir || 'out';
                const importDeps = [].concat(init_npm_1.deps);
                let importDevDeps = [].concat(init_npm_1.devDeps);
                let importExactDevDeps = [].concat(init_npm_1.exactDevDeps);
                let packageJSON = await (0, read_package_json_1.readRawPackageJson)(dir);
                if (!packageJSON.version) {
                    task.output = chalk_1.default.yellow(`Please set the ${chalk_1.default.green('"version"')} in your application's package.json`);
                }
                if (packageJSON.config && packageJSON.config.forge) {
                    if (packageJSON.config.forge.makers) {
                        task.output = chalk_1.default.green('Existing Electron Forge configuration detected');
                        if (typeof shouldContinueOnExisting === 'function') {
                            if (!(await shouldContinueOnExisting())) {
                                // TODO: figure out if we can just return early here
                                // eslint-disable-next-line no-process-exit
                                process.exit(0);
                            }
                        }
                    }
                    else if (!(typeof packageJSON.config.forge === 'object')) {
                        task.output = chalk_1.default.yellow("We can't tell if the Electron Forge config is compatible because it's in an external JavaScript file, not trying to convert it and continuing anyway");
                    }
                    else {
                        d('Upgrading an Electron Forge < 6 project');
                        packageJSON.config.forge = (0, upgrade_forge_config_1.default)(packageJSON.config.forge);
                        importDevDeps = (0, upgrade_forge_config_1.updateUpgradedForgeDevDeps)(packageJSON, importDevDeps);
                    }
                }
                packageJSON.dependencies = packageJSON.dependencies || {};
                packageJSON.devDependencies = packageJSON.devDependencies || {};
                [importDevDeps, importExactDevDeps] = (0, core_utils_1.updateElectronDependency)(packageJSON, importDevDeps, importExactDevDeps);
                const keys = Object.keys(packageJSON.dependencies).concat(Object.keys(packageJSON.devDependencies));
                const buildToolPackages = {
                    '@electron/get': 'already uses this module as a transitive dependency',
                    '@electron/osx-sign': 'already uses this module as a transitive dependency',
                    '@electron/packager': 'already uses this module as a transitive dependency',
                    'electron-builder': 'provides mostly equivalent functionality',
                    'electron-download': 'already uses this module as a transitive dependency',
                    'electron-forge': 'replaced with @electron-forge/cli',
                    'electron-installer-debian': 'already uses this module as a transitive dependency',
                    'electron-installer-dmg': 'already uses this module as a transitive dependency',
                    'electron-installer-flatpak': 'already uses this module as a transitive dependency',
                    'electron-installer-redhat': 'already uses this module as a transitive dependency',
                    'electron-winstaller': 'already uses this module as a transitive dependency',
                };
                for (const key of keys) {
                    if (buildToolPackages[key]) {
                        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                        const explanation = buildToolPackages[key];
                        let remove = true;
                        if (typeof shouldRemoveDependency === 'function') {
                            remove = await shouldRemoveDependency(key, explanation);
                        }
                        if (remove) {
                            delete packageJSON.dependencies[key];
                            delete packageJSON.devDependencies[key];
                        }
                    }
                }
                packageJSON.scripts = packageJSON.scripts || {};
                d('reading current scripts object:', packageJSON.scripts);
                const updatePackageScript = async (scriptName, newValue) => {
                    if (packageJSON.scripts[scriptName] !== newValue) {
                        let update = true;
                        if (typeof shouldUpdateScript === 'function') {
                            update = await shouldUpdateScript(scriptName, newValue);
                        }
                        if (update) {
                            packageJSON.scripts[scriptName] = newValue;
                        }
                    }
                };
                await updatePackageScript('start', 'electron-forge start');
                await updatePackageScript('package', 'electron-forge package');
                await updatePackageScript('make', 'electron-forge make');
                d('forgified scripts object:', packageJSON.scripts);
                const writeChanges = async () => {
                    await fs_extra_1.default.writeJson(node_path_1.default.resolve(dir, 'package.json'), packageJSON, { spaces: 2 });
                };
                return task.newListr([
                    {
                        title: `Resolving package manager`,
                        task: async (ctx, task) => {
                            ctx.pm = await (0, core_utils_1.resolvePackageManager)();
                            task.title = `Resolving package manager: ${chalk_1.default.cyan(ctx.pm.executable)}`;
                        },
                    },
                    {
                        title: 'Installing dependencies',
                        task: async ({ pm }, task) => {
                            await writeChanges();
                            d('deleting old dependencies forcefully');
                            await fs_extra_1.default.remove(node_path_1.default.resolve(dir, 'node_modules/.bin/electron'));
                            await fs_extra_1.default.remove(node_path_1.default.resolve(dir, 'node_modules/.bin/electron.cmd'));
                            d('installing dependencies');
                            task.output = `${pm.executable} ${pm.install} ${importDeps.join(' ')}`;
                            await (0, install_dependencies_1.default)(pm, dir, importDeps);
                            d('installing devDependencies');
                            task.output = `${pm.executable} ${pm.install} ${pm.dev} ${importDevDeps.join(' ')}`;
                            await (0, install_dependencies_1.default)(pm, dir, importDevDeps, install_dependencies_1.DepType.DEV);
                            d('installing devDependencies with exact versions');
                            task.output = `${pm.executable} ${pm.install} ${pm.dev} ${pm.exact} ${importExactDevDeps.join(' ')}`;
                            await (0, install_dependencies_1.default)(pm, dir, importExactDevDeps, install_dependencies_1.DepType.DEV, install_dependencies_1.DepVersionRestriction.EXACT);
                        },
                    },
                    {
                        title: 'Copying base template Forge configuration',
                        task: async () => {
                            const pathToTemplateConfig = node_path_1.default.resolve(template_base_1.default.templateDir, 'forge.config.js');
                            // if there's an existing config.forge object in package.json
                            if (packageJSON?.config?.forge && typeof packageJSON.config.forge === 'object') {
                                d('detected existing Forge config in package.json, merging with base template Forge config');
                                // eslint-disable-next-line @typescript-eslint/no-require-imports
                                const templateConfig = require(node_path_1.default.resolve(template_base_1.default.templateDir, 'forge.config.js'));
                                packageJSON = await (0, read_package_json_1.readRawPackageJson)(dir);
                                (0, lodash_1.merge)(templateConfig, packageJSON.config.forge); // mutates the templateConfig object
                                await writeChanges();
                                // otherwise, write to forge.config.js
                            }
                            else {
                                d('writing new forge.config.js');
                                await fs_extra_1.default.copyFile(pathToTemplateConfig, node_path_1.default.resolve(dir, 'forge.config.js'));
                            }
                        },
                    },
                    {
                        title: 'Fixing .gitignore',
                        task: async () => {
                            if (await fs_extra_1.default.pathExists(node_path_1.default.resolve(dir, '.gitignore'))) {
                                const gitignore = await fs_extra_1.default.readFile(node_path_1.default.resolve(dir, '.gitignore'));
                                if (!gitignore.includes(calculatedOutDir)) {
                                    await fs_extra_1.default.writeFile(node_path_1.default.resolve(dir, '.gitignore'), `${gitignore}\n${calculatedOutDir}/`);
                                }
                            }
                        },
                    },
                ], listrOptions);
            }),
        },
        {
            title: 'Finalizing import',
            rendererOptions: {
                persistentOutput: true,
                bottomBar: Infinity,
            },
            task: childTrace({ name: 'finalize-import', category: '@electron-forge/core' }, (_, __, task) => {
                task.output = `We have attempted to convert your app to be in a format that Electron Forge understands.
          
          Thanks for using ${chalk_1.default.green('Electron Forge')}!`;
            }),
        },
    ], listrOptions);
    await runner.run();
});
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW1wb3J0LmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vc3JjL2FwaS9pbXBvcnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLDBEQUE2QjtBQUU3QiwyREFBd0c7QUFFeEcsa0ZBQXlEO0FBQ3pELG1EQUFtRDtBQUNuRCxrREFBMEI7QUFDMUIsa0RBQTBCO0FBQzFCLHdEQUEwQjtBQUMxQixtQ0FBK0I7QUFDL0IsbUNBQStCO0FBRS9CLHFGQUE4RjtBQUM5RixpRUFBK0Q7QUFDL0QscUZBQThGO0FBRTlGLHNEQUFrRDtBQUNsRCxzREFBc0U7QUFFdEUsTUFBTSxDQUFDLEdBQUcsSUFBQSxlQUFLLEVBQUMsdUJBQXVCLENBQUMsQ0FBQztBQW1DekMsa0JBQWUsSUFBQSxrQkFBUyxFQUN0QixFQUFFLElBQUksRUFBRSxVQUFVLEVBQUUsUUFBUSxFQUFFLHNCQUFzQixFQUFFLEVBQ3RELEtBQUssRUFDSCxVQUFVLEVBQ1YsRUFBRSxHQUFHLEdBQUcsT0FBTyxDQUFDLEdBQUcsRUFBRSxFQUFFLFdBQVcsR0FBRyxLQUFLLEVBQUUsYUFBYSxFQUFFLHdCQUF3QixFQUFFLHNCQUFzQixFQUFFLGtCQUFrQixFQUFFLE1BQU0sRUFBaUIsRUFDekksRUFBRTtJQUNqQixNQUFNLFlBQVksR0FBeUM7UUFDekQsVUFBVSxFQUFFLEtBQUs7UUFDakIsZUFBZSxFQUFFO1lBQ2YsZ0JBQWdCLEVBQUUsS0FBSztZQUN2QixjQUFjLEVBQUUsS0FBSztTQUN0QjtRQUNELHVCQUF1QixFQUFFLENBQUMsV0FBVztRQUNyQyx5QkFBeUIsRUFBRSxPQUFPLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxLQUFLLENBQUMsSUFBSSxPQUFPLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUM7S0FDakYsQ0FBQztJQUVGLE1BQU0sTUFBTSxHQUFHLElBQUksY0FBSyxDQUN0QjtRQUNFO1lBQ0UsS0FBSyxFQUFFLDZCQUE2QjtZQUNwQyxJQUFJLEVBQUUsVUFBVSxDQUFDLEVBQUUsSUFBSSxFQUFFLGdCQUFnQixFQUFFLFFBQVEsRUFBRSxzQkFBc0IsRUFBRSxFQUFFLEtBQUssSUFBSSxFQUFFO2dCQUN4RixDQUFDLENBQUMsb0NBQW9DLEdBQUcsRUFBRSxDQUFDLENBQUM7Z0JBQzdDLElBQUksQ0FBQyxDQUFDLE1BQU0sa0JBQUUsQ0FBQyxVQUFVLENBQUMsR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsTUFBTSxrQkFBRSxDQUFDLFVBQVUsQ0FBQyxtQkFBSSxDQUFDLE9BQU8sQ0FBQyxHQUFHLEVBQUUsY0FBYyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUM7b0JBQzdGLE1BQU0sSUFBSSxLQUFLLENBQUMsMkRBQTJELEdBQUcsRUFBRSxDQUFDLENBQUM7Z0JBQ3BGLENBQUM7Z0JBRUQsSUFBSSxPQUFPLGFBQWEsS0FBSyxVQUFVLEVBQUUsQ0FBQztvQkFDeEMsSUFBSSxDQUFDLENBQUMsTUFBTSxhQUFhLEVBQUUsQ0FBQyxFQUFFLENBQUM7d0JBQzdCLG9EQUFvRDt3QkFDcEQsMkNBQTJDO3dCQUMzQyxPQUFPLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDO29CQUNsQixDQUFDO2dCQUNILENBQUM7Z0JBRUQsTUFBTSxJQUFBLGtCQUFPLEVBQUMsR0FBRyxDQUFDLENBQUM7WUFDckIsQ0FBQyxDQUFDO1NBQ0g7UUFDRDtZQUNFLEtBQUssRUFBRSwyQ0FBMkM7WUFDbEQsZUFBZSxFQUFFO2dCQUNmLGdCQUFnQixFQUFFLElBQUk7Z0JBQ3RCLFNBQVMsRUFBRSxRQUFRO2FBQ3BCO1lBQ0QsSUFBSSxFQUFFLFVBQVUsQ0FBK0IsRUFBRSxJQUFJLEVBQUUsUUFBUSxFQUFFLFFBQVEsRUFBRSxLQUFLLEVBQUUsRUFBRSxLQUFLLEVBQUUsQ0FBQyxFQUFFLEdBQUcsRUFBRSxJQUFJLEVBQUUsRUFBRTtnQkFDekcsTUFBTSxnQkFBZ0IsR0FBRyxNQUFNLElBQUksS0FBSyxDQUFDO2dCQUV6QyxNQUFNLFVBQVUsR0FBSSxFQUFlLENBQUMsTUFBTSxDQUFDLGVBQUksQ0FBQyxDQUFDO2dCQUNqRCxJQUFJLGFBQWEsR0FBSSxFQUFlLENBQUMsTUFBTSxDQUFDLGtCQUFPLENBQUMsQ0FBQztnQkFDckQsSUFBSSxrQkFBa0IsR0FBSSxFQUFlLENBQUMsTUFBTSxDQUFDLHVCQUFZLENBQUMsQ0FBQztnQkFFL0QsSUFBSSxXQUFXLEdBQUcsTUFBTSxJQUFBLHNDQUFrQixFQUFDLEdBQUcsQ0FBQyxDQUFDO2dCQUNoRCxJQUFJLENBQUMsV0FBVyxDQUFDLE9BQU8sRUFBRSxDQUFDO29CQUN6QixJQUFJLENBQUMsTUFBTSxHQUFHLGVBQUssQ0FBQyxNQUFNLENBQUMsa0JBQWtCLGVBQUssQ0FBQyxLQUFLLENBQUMsV0FBVyxDQUFDLHFDQUFxQyxDQUFDLENBQUM7Z0JBQzlHLENBQUM7Z0JBQ0QsSUFBSSxXQUFXLENBQUMsTUFBTSxJQUFJLFdBQVcsQ0FBQyxNQUFNLENBQUMsS0FBSyxFQUFFLENBQUM7b0JBQ25ELElBQUksV0FBVyxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsTUFBTSxFQUFFLENBQUM7d0JBQ3BDLElBQUksQ0FBQyxNQUFNLEdBQUcsZUFBSyxDQUFDLEtBQUssQ0FBQyxnREFBZ0QsQ0FBQyxDQUFDO3dCQUM1RSxJQUFJLE9BQU8sd0JBQXdCLEtBQUssVUFBVSxFQUFFLENBQUM7NEJBQ25ELElBQUksQ0FBQyxDQUFDLE1BQU0sd0JBQXdCLEVBQUUsQ0FBQyxFQUFFLENBQUM7Z0NBQ3hDLG9EQUFvRDtnQ0FDcEQsMkNBQTJDO2dDQUMzQyxPQUFPLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDOzRCQUNsQixDQUFDO3dCQUNILENBQUM7b0JBQ0gsQ0FBQzt5QkFBTSxJQUFJLENBQUMsQ0FBQyxPQUFPLFdBQVcsQ0FBQyxNQUFNLENBQUMsS0FBSyxLQUFLLFFBQVEsQ0FBQyxFQUFFLENBQUM7d0JBQzNELElBQUksQ0FBQyxNQUFNLEdBQUcsZUFBSyxDQUFDLE1BQU0sQ0FDeEIsc0pBQXNKLENBQ3ZKLENBQUM7b0JBQ0osQ0FBQzt5QkFBTSxDQUFDO3dCQUNOLENBQUMsQ0FBQyx5Q0FBeUMsQ0FBQyxDQUFDO3dCQUM3QyxXQUFXLENBQUMsTUFBTSxDQUFDLEtBQUssR0FBRyxJQUFBLDhCQUFrQixFQUFDLFdBQVcsQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLENBQUM7d0JBQ3hFLGFBQWEsR0FBRyxJQUFBLGlEQUEwQixFQUFDLFdBQVcsRUFBRSxhQUFhLENBQUMsQ0FBQztvQkFDekUsQ0FBQztnQkFDSCxDQUFDO2dCQUVELFdBQVcsQ0FBQyxZQUFZLEdBQUcsV0FBVyxDQUFDLFlBQVksSUFBSSxFQUFFLENBQUM7Z0JBQzFELFdBQVcsQ0FBQyxlQUFlLEdBQUcsV0FBVyxDQUFDLGVBQWUsSUFBSSxFQUFFLENBQUM7Z0JBRWhFLENBQUMsYUFBYSxFQUFFLGtCQUFrQixDQUFDLEdBQUcsSUFBQSxxQ0FBd0IsRUFBQyxXQUFXLEVBQUUsYUFBYSxFQUFFLGtCQUFrQixDQUFDLENBQUM7Z0JBRS9HLE1BQU0sSUFBSSxHQUFHLE1BQU0sQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLFlBQVksQ0FBQyxDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxlQUFlLENBQUMsQ0FBQyxDQUFDO2dCQUNwRyxNQUFNLGlCQUFpQixHQUF1QztvQkFDNUQsZUFBZSxFQUFFLHFEQUFxRDtvQkFDdEUsb0JBQW9CLEVBQUUscURBQXFEO29CQUMzRSxvQkFBb0IsRUFBRSxxREFBcUQ7b0JBQzNFLGtCQUFrQixFQUFFLDBDQUEwQztvQkFDOUQsbUJBQW1CLEVBQUUscURBQXFEO29CQUMxRSxnQkFBZ0IsRUFBRSxtQ0FBbUM7b0JBQ3JELDJCQUEyQixFQUFFLHFEQUFxRDtvQkFDbEYsd0JBQXdCLEVBQUUscURBQXFEO29CQUMvRSw0QkFBNEIsRUFBRSxxREFBcUQ7b0JBQ25GLDJCQUEyQixFQUFFLHFEQUFxRDtvQkFDbEYscUJBQXFCLEVBQUUscURBQXFEO2lCQUM3RSxDQUFDO2dCQUVGLEtBQUssTUFBTSxHQUFHLElBQUksSUFBSSxFQUFFLENBQUM7b0JBQ3ZCLElBQUksaUJBQWlCLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQzt3QkFDM0Isb0VBQW9FO3dCQUNwRSxNQUFNLFdBQVcsR0FBRyxpQkFBaUIsQ0FBQyxHQUFHLENBQUUsQ0FBQzt3QkFDNUMsSUFBSSxNQUFNLEdBQUcsSUFBSSxDQUFDO3dCQUNsQixJQUFJLE9BQU8sc0JBQXNCLEtBQUssVUFBVSxFQUFFLENBQUM7NEJBQ2pELE1BQU0sR0FBRyxNQUFNLHNCQUFzQixDQUFDLEdBQUcsRUFBRSxXQUFXLENBQUMsQ0FBQzt3QkFDMUQsQ0FBQzt3QkFFRCxJQUFJLE1BQU0sRUFBRSxDQUFDOzRCQUNYLE9BQU8sV0FBVyxDQUFDLFlBQVksQ0FBQyxHQUFHLENBQUMsQ0FBQzs0QkFDckMsT0FBTyxXQUFXLENBQUMsZUFBZSxDQUFDLEdBQUcsQ0FBQyxDQUFDO3dCQUMxQyxDQUFDO29CQUNILENBQUM7Z0JBQ0gsQ0FBQztnQkFFRCxXQUFXLENBQUMsT0FBTyxHQUFHLFdBQVcsQ0FBQyxPQUFPLElBQUksRUFBRSxDQUFDO2dCQUNoRCxDQUFDLENBQUMsaUNBQWlDLEVBQUUsV0FBVyxDQUFDLE9BQU8sQ0FBQyxDQUFDO2dCQUUxRCxNQUFNLG1CQUFtQixHQUFHLEtBQUssRUFBRSxVQUFrQixFQUFFLFFBQWdCLEVBQUUsRUFBRTtvQkFDekUsSUFBSSxXQUFXLENBQUMsT0FBTyxDQUFDLFVBQVUsQ0FBQyxLQUFLLFFBQVEsRUFBRSxDQUFDO3dCQUNqRCxJQUFJLE1BQU0sR0FBRyxJQUFJLENBQUM7d0JBQ2xCLElBQUksT0FBTyxrQkFBa0IsS0FBSyxVQUFVLEVBQUUsQ0FBQzs0QkFDN0MsTUFBTSxHQUFHLE1BQU0sa0JBQWtCLENBQUMsVUFBVSxFQUFFLFFBQVEsQ0FBQyxDQUFDO3dCQUMxRCxDQUFDO3dCQUNELElBQUksTUFBTSxFQUFFLENBQUM7NEJBQ1gsV0FBVyxDQUFDLE9BQU8sQ0FBQyxVQUFVLENBQUMsR0FBRyxRQUFRLENBQUM7d0JBQzdDLENBQUM7b0JBQ0gsQ0FBQztnQkFDSCxDQUFDLENBQUM7Z0JBRUYsTUFBTSxtQkFBbUIsQ0FBQyxPQUFPLEVBQUUsc0JBQXNCLENBQUMsQ0FBQztnQkFDM0QsTUFBTSxtQkFBbUIsQ0FBQyxTQUFTLEVBQUUsd0JBQXdCLENBQUMsQ0FBQztnQkFDL0QsTUFBTSxtQkFBbUIsQ0FBQyxNQUFNLEVBQUUscUJBQXFCLENBQUMsQ0FBQztnQkFFekQsQ0FBQyxDQUFDLDJCQUEyQixFQUFFLFdBQVcsQ0FBQyxPQUFPLENBQUMsQ0FBQztnQkFFcEQsTUFBTSxZQUFZLEdBQUcsS0FBSyxJQUFJLEVBQUU7b0JBQzlCLE1BQU0sa0JBQUUsQ0FBQyxTQUFTLENBQUMsbUJBQUksQ0FBQyxPQUFPLENBQUMsR0FBRyxFQUFFLGNBQWMsQ0FBQyxFQUFFLFdBQVcsRUFBRSxFQUFFLE1BQU0sRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDO2dCQUNwRixDQUFDLENBQUM7Z0JBRUYsT0FBTyxJQUFJLENBQUMsUUFBUSxDQUNsQjtvQkFDRTt3QkFDRSxLQUFLLEVBQUUsMkJBQTJCO3dCQUNsQyxJQUFJLEVBQUUsS0FBSyxFQUFFLEdBQUcsRUFBRSxJQUFJLEVBQUUsRUFBRTs0QkFDeEIsR0FBRyxDQUFDLEVBQUUsR0FBRyxNQUFNLElBQUEsa0NBQXFCLEdBQUUsQ0FBQzs0QkFDdkMsSUFBSSxDQUFDLEtBQUssR0FBRyw4QkFBOEIsZUFBSyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLFVBQVUsQ0FBQyxFQUFFLENBQUM7d0JBQzdFLENBQUM7cUJBQ0Y7b0JBQ0Q7d0JBQ0UsS0FBSyxFQUFFLHlCQUF5Qjt3QkFDaEMsSUFBSSxFQUFFLEtBQUssRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLElBQUksRUFBRSxFQUFFOzRCQUMzQixNQUFNLFlBQVksRUFBRSxDQUFDOzRCQUVyQixDQUFDLENBQUMsc0NBQXNDLENBQUMsQ0FBQzs0QkFDMUMsTUFBTSxrQkFBRSxDQUFDLE1BQU0sQ0FBQyxtQkFBSSxDQUFDLE9BQU8sQ0FBQyxHQUFHLEVBQUUsNEJBQTRCLENBQUMsQ0FBQyxDQUFDOzRCQUNqRSxNQUFNLGtCQUFFLENBQUMsTUFBTSxDQUFDLG1CQUFJLENBQUMsT0FBTyxDQUFDLEdBQUcsRUFBRSxnQ0FBZ0MsQ0FBQyxDQUFDLENBQUM7NEJBRXJFLENBQUMsQ0FBQyx5QkFBeUIsQ0FBQyxDQUFDOzRCQUM3QixJQUFJLENBQUMsTUFBTSxHQUFHLEdBQUcsRUFBRSxDQUFDLFVBQVUsSUFBSSxFQUFFLENBQUMsT0FBTyxJQUFJLFVBQVUsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQzs0QkFDdkUsTUFBTSxJQUFBLDhCQUFjLEVBQUMsRUFBRSxFQUFFLEdBQUcsRUFBRSxVQUFVLENBQUMsQ0FBQzs0QkFFMUMsQ0FBQyxDQUFDLDRCQUE0QixDQUFDLENBQUM7NEJBQ2hDLElBQUksQ0FBQyxNQUFNLEdBQUcsR0FBRyxFQUFFLENBQUMsVUFBVSxJQUFJLEVBQUUsQ0FBQyxPQUFPLElBQUksRUFBRSxDQUFDLEdBQUcsSUFBSSxhQUFhLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUM7NEJBQ3BGLE1BQU0sSUFBQSw4QkFBYyxFQUFDLEVBQUUsRUFBRSxHQUFHLEVBQUUsYUFBYSxFQUFFLDhCQUFPLENBQUMsR0FBRyxDQUFDLENBQUM7NEJBRTFELENBQUMsQ0FBQyxnREFBZ0QsQ0FBQyxDQUFDOzRCQUNwRCxJQUFJLENBQUMsTUFBTSxHQUFHLEdBQUcsRUFBRSxDQUFDLFVBQVUsSUFBSSxFQUFFLENBQUMsT0FBTyxJQUFJLEVBQUUsQ0FBQyxHQUFHLElBQUksRUFBRSxDQUFDLEtBQUssSUFBSSxrQkFBa0IsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQzs0QkFDckcsTUFBTSxJQUFBLDhCQUFjLEVBQUMsRUFBRSxFQUFFLEdBQUcsRUFBRSxrQkFBa0IsRUFBRSw4QkFBTyxDQUFDLEdBQUcsRUFBRSw0Q0FBcUIsQ0FBQyxLQUFLLENBQUMsQ0FBQzt3QkFDOUYsQ0FBQztxQkFDRjtvQkFDRDt3QkFDRSxLQUFLLEVBQUUsMkNBQTJDO3dCQUNsRCxJQUFJLEVBQUUsS0FBSyxJQUFJLEVBQUU7NEJBQ2YsTUFBTSxvQkFBb0IsR0FBRyxtQkFBSSxDQUFDLE9BQU8sQ0FBQyx1QkFBWSxDQUFDLFdBQVcsRUFBRSxpQkFBaUIsQ0FBQyxDQUFDOzRCQUV2Riw2REFBNkQ7NEJBQzdELElBQUksV0FBVyxFQUFFLE1BQU0sRUFBRSxLQUFLLElBQUksT0FBTyxXQUFXLENBQUMsTUFBTSxDQUFDLEtBQUssS0FBSyxRQUFRLEVBQUUsQ0FBQztnQ0FDL0UsQ0FBQyxDQUFDLHlGQUF5RixDQUFDLENBQUM7Z0NBQzdGLGlFQUFpRTtnQ0FDakUsTUFBTSxjQUFjLEdBQUcsT0FBTyxDQUFDLG1CQUFJLENBQUMsT0FBTyxDQUFDLHVCQUFZLENBQUMsV0FBVyxFQUFFLGlCQUFpQixDQUFDLENBQUMsQ0FBQztnQ0FDMUYsV0FBVyxHQUFHLE1BQU0sSUFBQSxzQ0FBa0IsRUFBQyxHQUFHLENBQUMsQ0FBQztnQ0FDNUMsSUFBQSxjQUFLLEVBQUMsY0FBYyxFQUFFLFdBQVcsQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxvQ0FBb0M7Z0NBQ3JGLE1BQU0sWUFBWSxFQUFFLENBQUM7Z0NBQ3JCLHNDQUFzQzs0QkFDeEMsQ0FBQztpQ0FBTSxDQUFDO2dDQUNOLENBQUMsQ0FBQyw2QkFBNkIsQ0FBQyxDQUFDO2dDQUNqQyxNQUFNLGtCQUFFLENBQUMsUUFBUSxDQUFDLG9CQUFvQixFQUFFLG1CQUFJLENBQUMsT0FBTyxDQUFDLEdBQUcsRUFBRSxpQkFBaUIsQ0FBQyxDQUFDLENBQUM7NEJBQ2hGLENBQUM7d0JBQ0gsQ0FBQztxQkFDRjtvQkFDRDt3QkFDRSxLQUFLLEVBQUUsbUJBQW1CO3dCQUMxQixJQUFJLEVBQUUsS0FBSyxJQUFJLEVBQUU7NEJBQ2YsSUFBSSxNQUFNLGtCQUFFLENBQUMsVUFBVSxDQUFDLG1CQUFJLENBQUMsT0FBTyxDQUFDLEdBQUcsRUFBRSxZQUFZLENBQUMsQ0FBQyxFQUFFLENBQUM7Z0NBQ3pELE1BQU0sU0FBUyxHQUFHLE1BQU0sa0JBQUUsQ0FBQyxRQUFRLENBQUMsbUJBQUksQ0FBQyxPQUFPLENBQUMsR0FBRyxFQUFFLFlBQVksQ0FBQyxDQUFDLENBQUM7Z0NBQ3JFLElBQUksQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLGdCQUFnQixDQUFDLEVBQUUsQ0FBQztvQ0FDMUMsTUFBTSxrQkFBRSxDQUFDLFNBQVMsQ0FBQyxtQkFBSSxDQUFDLE9BQU8sQ0FBQyxHQUFHLEVBQUUsWUFBWSxDQUFDLEVBQUUsR0FBRyxTQUFTLEtBQUssZ0JBQWdCLEdBQUcsQ0FBQyxDQUFDO2dDQUM1RixDQUFDOzRCQUNILENBQUM7d0JBQ0gsQ0FBQztxQkFDRjtpQkFDRixFQUNELFlBQVksQ0FDYixDQUFDO1lBQ0osQ0FBQyxDQUFDO1NBQ0g7UUFDRDtZQUNFLEtBQUssRUFBRSxtQkFBbUI7WUFDMUIsZUFBZSxFQUFFO2dCQUNmLGdCQUFnQixFQUFFLElBQUk7Z0JBQ3RCLFNBQVMsRUFBRSxRQUFRO2FBQ3BCO1lBQ0QsSUFBSSxFQUFFLFVBQVUsQ0FBK0IsRUFBRSxJQUFJLEVBQUUsaUJBQWlCLEVBQUUsUUFBUSxFQUFFLHNCQUFzQixFQUFFLEVBQUUsQ0FBQyxDQUFDLEVBQUUsRUFBRSxFQUFFLElBQUksRUFBRSxFQUFFO2dCQUM1SCxJQUFJLENBQUMsTUFBTSxHQUFHOzs2QkFFRyxlQUFLLENBQUMsS0FBSyxDQUFDLGdCQUFnQixDQUFDLEdBQUcsQ0FBQztZQUNwRCxDQUFDLENBQUM7U0FDSDtLQUNGLEVBQ0QsWUFBWSxDQUNiLENBQUM7SUFFRixNQUFNLE1BQU0sQ0FBQyxHQUFHLEVBQUUsQ0FBQztBQUNyQixDQUFDLENBQ0YsQ0FBQyJ9