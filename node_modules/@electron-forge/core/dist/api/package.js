"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.listrPackage = void 0;
const node_path_1 = __importDefault(require("node:path"));
const node_util_1 = require("node:util");
const get_1 = require("@electron/get");
const packager_1 = require("@electron/packager");
const core_utils_1 = require("@electron-forge/core-utils");
const tracer_1 = require("@electron-forge/tracer");
const chalk_1 = __importDefault(require("chalk"));
const debug_1 = __importDefault(require("debug"));
const fast_glob_1 = __importDefault(require("fast-glob"));
const fs_extra_1 = __importDefault(require("fs-extra"));
const listr2_1 = require("listr2");
const forge_config_1 = __importDefault(require("../util/forge-config"));
const hook_1 = require("../util/hook");
const import_search_1 = __importDefault(require("../util/import-search"));
const messages_1 = require("../util/messages");
const out_dir_1 = __importDefault(require("../util/out-dir"));
const read_package_json_1 = require("../util/read-package-json");
const resolve_dir_1 = __importDefault(require("../util/resolve-dir"));
const d = (0, debug_1.default)('electron-forge:packager');
/**
 * Resolves hooks if they are a path to a file (instead of a `Function`).
 */
async function resolveHooks(hooks, dir) {
    if (hooks) {
        return await Promise.all(hooks.map(async (hook) => (typeof hook === 'string' ? (await (0, import_search_1.default)(dir, [hook])) : hook)));
    }
    return [];
}
/**
 * Runs given hooks sequentially by mapping them to promises and iterating
 * through while awaiting
 */
function sequentialHooks(hooks) {
    return [
        async (buildPath, electronVersion, platform, arch, done) => {
            for (const hook of hooks) {
                try {
                    await (0, node_util_1.promisify)(hook)(buildPath, electronVersion, platform, arch);
                }
                catch (err) {
                    d('hook failed:', hook.toString(), err);
                    return done(err);
                }
            }
            done();
        },
    ];
}
function sequentialFinalizePackageTargetsHooks(hooks) {
    return [
        async (targets, done) => {
            for (const hook of hooks) {
                try {
                    await (0, node_util_1.promisify)(hook)(targets);
                }
                catch (err) {
                    return done(err);
                }
            }
            done();
        },
    ];
}
const listrPackage = (childTrace, { dir: providedDir = process.cwd(), interactive = false, arch = (0, get_1.getHostArch)(), platform = process.platform, outDir, }) => {
    const runner = new listr2_1.Listr([
        {
            title: 'Preparing to package application',
            task: childTrace({ name: 'package-prepare', category: '@electron-forge/core' }, async (_, ctx) => {
                const resolvedDir = await (0, resolve_dir_1.default)(providedDir);
                if (!resolvedDir) {
                    throw new Error('Failed to locate compilable Electron application');
                }
                ctx.dir = resolvedDir;
                ctx.forgeConfig = await (0, forge_config_1.default)(resolvedDir);
                ctx.packageJSON = await (0, read_package_json_1.readMutatedPackageJson)(resolvedDir, ctx.forgeConfig);
                if (!ctx.packageJSON.main) {
                    throw new Error('packageJSON.main must be set to a valid entry point for your Electron app');
                }
                ctx.calculatedOutDir = outDir || (0, out_dir_1.default)(resolvedDir, ctx.forgeConfig);
            }),
        },
        {
            title: 'Running packaging hooks',
            task: childTrace({ name: 'run-packaging-hooks', category: '@electron-forge/core' }, async (childTrace, { forgeConfig }, task) => {
                return (0, tracer_1.delayTraceTillSignal)(childTrace, task.newListr([
                    {
                        title: `Running ${chalk_1.default.yellow('generateAssets')} hook`,
                        task: childTrace({ name: 'run-generateAssets-hook', category: '@electron-forge/core' }, async (childTrace, _, task) => {
                            return (0, tracer_1.delayTraceTillSignal)(childTrace, task.newListr(await (0, hook_1.getHookListrTasks)(childTrace, forgeConfig, 'generateAssets', platform, arch)), 'run');
                        }),
                    },
                    {
                        title: `Running ${chalk_1.default.yellow('prePackage')} hook`,
                        task: childTrace({ name: 'run-prePackage-hook', category: '@electron-forge/core' }, async (childTrace, _, task) => {
                            return (0, tracer_1.delayTraceTillSignal)(childTrace, task.newListr(await (0, hook_1.getHookListrTasks)(childTrace, forgeConfig, 'prePackage', platform, arch)), 'run');
                        }),
                    },
                ]), 'run');
            }),
        },
        {
            title: 'Packaging application',
            task: childTrace({ name: 'packaging-application', category: '@electron-forge/core' }, async (childTrace, ctx, task) => {
                const { calculatedOutDir, forgeConfig, packageJSON } = ctx;
                const getTargetKey = (target) => `${target.platform}/${target.arch}`;
                task.output = 'Determining targets...';
                const signalCopyDone = new Map();
                const signalRebuildDone = new Map();
                const signalPackageDone = new Map();
                const rejects = [];
                const signalDone = (map, target) => {
                    map.get(getTargetKey(target))?.pop()?.();
                };
                const addSignalAndWait = async (map, target) => {
                    const targetKey = getTargetKey(target);
                    await new Promise((resolve, reject) => {
                        rejects.push(reject);
                        map.set(targetKey, (map.get(targetKey) || []).concat([resolve]));
                    });
                };
                let provideTargets;
                const targetsPromise = new Promise((resolve, reject) => {
                    provideTargets = resolve;
                    rejects.push(reject);
                });
                const rebuildTasks = new Map();
                const signalRebuildStart = new Map();
                const afterFinalizePackageTargetsHooks = [
                    (targets, done) => {
                        provideTargets(targets);
                        done();
                    },
                    ...(await resolveHooks(forgeConfig.packagerConfig.afterFinalizePackageTargets, ctx.dir)),
                ];
                const pruneEnabled = !('prune' in forgeConfig.packagerConfig) || forgeConfig.packagerConfig.prune;
                const afterCopyHooks = [
                    async (buildPath, electronVersion, platform, arch, done) => {
                        signalDone(signalCopyDone, { platform, arch });
                        done();
                    },
                    async (buildPath, electronVersion, pPlatform, pArch, done) => {
                        const bins = await (0, fast_glob_1.default)(node_path_1.default.join(buildPath, '**/.bin/**/*'));
                        for (const bin of bins) {
                            await fs_extra_1.default.remove(bin);
                        }
                        done();
                    },
                    async (buildPath, electronVersion, pPlatform, pArch, done) => {
                        await (0, hook_1.runHook)(forgeConfig, 'packageAfterCopy', buildPath, electronVersion, pPlatform, pArch);
                        done();
                    },
                    async (buildPath, electronVersion, pPlatform, pArch, done) => {
                        const targetKey = getTargetKey({ platform: pPlatform, arch: pArch });
                        await (0, core_utils_1.listrCompatibleRebuildHook)(buildPath, electronVersion, pPlatform, pArch, forgeConfig.rebuildConfig, 
                        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                        await rebuildTasks.get(targetKey).pop());
                        signalRebuildDone.get(targetKey)?.pop()?.();
                        done();
                    },
                    async (buildPath, electronVersion, pPlatform, pArch, done) => {
                        const copiedPackageJSON = await (0, read_package_json_1.readMutatedPackageJson)(buildPath, forgeConfig);
                        if (copiedPackageJSON.config && copiedPackageJSON.config.forge) {
                            delete copiedPackageJSON.config.forge;
                        }
                        await fs_extra_1.default.writeJson(node_path_1.default.resolve(buildPath, 'package.json'), copiedPackageJSON, { spaces: 2 });
                        done();
                    },
                    ...(await resolveHooks(forgeConfig.packagerConfig.afterCopy, ctx.dir)),
                ];
                const afterCompleteHooks = [
                    async (buildPath, electronVersion, pPlatform, pArch, done) => {
                        signalPackageDone.get(getTargetKey({ platform: pPlatform, arch: pArch }))?.pop()?.();
                        done();
                    },
                    ...(await resolveHooks(forgeConfig.packagerConfig.afterComplete, ctx.dir)),
                ];
                const afterPruneHooks = [];
                if (pruneEnabled) {
                    afterPruneHooks.push(...(await resolveHooks(forgeConfig.packagerConfig.afterPrune, ctx.dir)));
                }
                afterPruneHooks.push((async (buildPath, electronVersion, pPlatform, pArch, done) => {
                    await (0, hook_1.runHook)(forgeConfig, 'packageAfterPrune', buildPath, electronVersion, pPlatform, pArch);
                    done();
                }));
                const afterExtractHooks = [
                    (async (buildPath, electronVersion, pPlatform, pArch, done) => {
                        await (0, hook_1.runHook)(forgeConfig, 'packageAfterExtract', buildPath, electronVersion, pPlatform, pArch);
                        done();
                    }),
                ];
                afterExtractHooks.push(...(await resolveHooks(forgeConfig.packagerConfig.afterExtract, ctx.dir)));
                const packageOpts = {
                    asar: false,
                    overwrite: true,
                    ignore: [/^\/out\//g],
                    quiet: true,
                    ...forgeConfig.packagerConfig,
                    dir: ctx.dir,
                    arch: arch,
                    platform,
                    afterFinalizePackageTargets: sequentialFinalizePackageTargetsHooks(afterFinalizePackageTargetsHooks),
                    afterComplete: sequentialHooks(afterCompleteHooks),
                    afterCopy: sequentialHooks(afterCopyHooks),
                    afterExtract: sequentialHooks(afterExtractHooks),
                    afterPrune: sequentialHooks(afterPruneHooks),
                    out: calculatedOutDir,
                    electronVersion: await (0, core_utils_1.getElectronVersion)(ctx.dir, packageJSON),
                };
                if (packageOpts.all) {
                    throw new Error('config.forge.packagerConfig.all is not supported by Electron Forge');
                }
                if (!packageJSON.version && !packageOpts.appVersion) {
                    (0, messages_1.warn)(interactive, chalk_1.default.yellow('Please set "version" or "config.forge.packagerConfig.appVersion" in your application\'s package.json so auto-updates work properly'));
                }
                if (packageOpts.prebuiltAsar) {
                    throw new Error('config.forge.packagerConfig.prebuiltAsar is not supported by Electron Forge');
                }
                d('packaging with options', packageOpts);
                ctx.packagerPromise = (0, packager_1.packager)(packageOpts);
                // Handle error by failing this task
                // rejects is populated by the reject handlers for every
                // signal based promise in every subtask
                ctx.packagerPromise.catch((err) => {
                    for (const reject of rejects) {
                        reject(err);
                    }
                });
                const targets = await targetsPromise;
                // Copy the resolved targets into the context for later
                ctx.targets = [...targets];
                // If we are targetting a universal build we need to add the "fake"
                // x64 and arm64 builds into the list of targets so that we can
                // show progress for those
                for (const target of targets) {
                    if (target.arch === 'universal') {
                        targets.push({
                            platform: target.platform,
                            arch: 'x64',
                            forUniversal: true,
                        }, {
                            platform: target.platform,
                            arch: 'arm64',
                            forUniversal: true,
                        });
                    }
                }
                // Populate rebuildTasks with promises that resolve with the rebuild tasks
                // that will eventually run
                for (const target of targets) {
                    // Skip universal tasks as they do not have rebuild sub-tasks
                    if (target.arch === 'universal')
                        continue;
                    const targetKey = getTargetKey(target);
                    rebuildTasks.set(targetKey, (rebuildTasks.get(targetKey) || []).concat([
                        new Promise((resolve) => {
                            signalRebuildStart.set(targetKey, (signalRebuildStart.get(targetKey) || []).concat([resolve]));
                        }),
                    ]));
                }
                d('targets:', targets);
                return (0, tracer_1.delayTraceTillSignal)(childTrace, task.newListr(targets.map((target) => target.arch === 'universal'
                    ? {
                        title: `Stitching ${chalk_1.default.cyan(`${target.platform}/x64`)} and ${chalk_1.default.cyan(`${target.platform}/arm64`)} into a ${chalk_1.default.green(`${target.platform}/universal`)} package`,
                        task: async () => {
                            await addSignalAndWait(signalPackageDone, target);
                        },
                        rendererOptions: {
                            timer: { ...listr2_1.PRESET_TIMER },
                        },
                    }
                    : {
                        title: `Packaging for ${chalk_1.default.cyan(target.arch)} on ${chalk_1.default.cyan(target.platform)}${target.forUniversal ? chalk_1.default.italic(' (for universal package)') : ''}`,
                        task: childTrace({
                            name: `package-app-${target.platform}-${target.arch}${target.forUniversal ? '-universal-tmp' : ''}`,
                            category: '@electron-forge/core',
                            extraDetails: { arch: target.arch, platform: target.platform },
                            newRoot: true,
                        }, async (childTrace, _, task) => {
                            return (0, tracer_1.delayTraceTillSignal)(childTrace, task.newListr([
                                {
                                    title: 'Copying files',
                                    task: childTrace({ name: 'copy-files', category: '@electron-forge/core' }, async () => {
                                        await addSignalAndWait(signalCopyDone, target);
                                    }),
                                },
                                {
                                    title: 'Preparing native dependencies',
                                    task: childTrace({ name: 'prepare-native-dependencies', category: '@electron-forge/core' }, async (_, __, task) => {
                                        signalRebuildStart.get(getTargetKey(target))?.pop()?.(task);
                                        await addSignalAndWait(signalRebuildDone, target);
                                    }),
                                    rendererOptions: {
                                        persistentOutput: true,
                                        bottomBar: Infinity,
                                        timer: { ...listr2_1.PRESET_TIMER },
                                    },
                                },
                                {
                                    title: 'Finalizing package',
                                    task: childTrace({ name: 'finalize-package', category: '@electron-forge/core' }, async () => {
                                        await addSignalAndWait(signalPackageDone, target);
                                    }),
                                },
                            ], { rendererOptions: { collapseSubtasks: true, collapseErrors: false } }), 'run');
                        }),
                        rendererOptions: {
                            timer: { ...listr2_1.PRESET_TIMER },
                        },
                    }), { concurrent: true, rendererOptions: { collapseSubtasks: false, collapseErrors: false } }), 'run');
            }),
        },
        {
            title: `Running ${chalk_1.default.yellow('postPackage')} hook`,
            task: childTrace({ name: 'run-postPackage-hook', category: '@electron-forge/core' }, async (childTrace, { packagerPromise, forgeConfig }, task) => {
                const outputPaths = await packagerPromise;
                d('outputPaths:', outputPaths);
                return (0, tracer_1.delayTraceTillSignal)(childTrace, task.newListr(await (0, hook_1.getHookListrTasks)(childTrace, forgeConfig, 'postPackage', {
                    arch,
                    outputPaths,
                    platform,
                })), 'run');
            }),
        },
    ], {
        concurrent: false,
        silentRendererCondition: !interactive,
        fallbackRendererCondition: Boolean(process.env.DEBUG) || Boolean(process.env.CI),
        rendererOptions: {
            collapseSubtasks: false,
            collapseErrors: false,
        },
        ctx: {},
    });
    return runner;
};
exports.listrPackage = listrPackage;
exports.default = (0, tracer_1.autoTrace)({ name: 'package()', category: '@electron-forge/core' }, async (childTrace, opts) => {
    const runner = (0, exports.listrPackage)(childTrace, opts);
    await runner.run();
    const outputPaths = await runner.ctx.packagerPromise;
    return runner.ctx.targets.map((target, index) => ({
        platform: target.platform,
        arch: target.arch,
        packagedPath: outputPaths[index],
    }));
});
//# sourceMappingURL=data:application/json;base64,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