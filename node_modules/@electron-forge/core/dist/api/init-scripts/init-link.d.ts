import { PMDetails } from '@electron-forge/core-utils';
import { ForgeListrTask } from '@electron-forge/shared-types';
/**
 * Link local forge dependencies
 *
 * This allows developers working on forge itself to easily init
 * a local template and have it use their local plugins / core / cli packages.
 *
 * Note: `yarn link:prepare` needs to run first before dependencies can be
 * linked.
 */
export declare function initLink<T>(pm: PMDetails, dir: string, task?: ForgeListrTask<T>): Promise<void>;
//# sourceMappingURL=init-link.d.ts.map