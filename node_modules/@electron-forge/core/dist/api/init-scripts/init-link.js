"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.initLink = void 0;
const node_path_1 = __importDefault(require("node:path"));
const core_utils_1 = require("@electron-forge/core-utils");
const debug_1 = __importDefault(require("debug"));
const read_package_json_1 = require("../../util/read-package-json");
const d = (0, debug_1.default)('electron-forge:init:link');
/**
 * Link local forge dependencies
 *
 * This allows developers working on forge itself to easily init
 * a local template and have it use their local plugins / core / cli packages.
 *
 * Note: `yarn link:prepare` needs to run first before dependencies can be
 * linked.
 */
async function initLink(pm, dir, task) {
    const shouldLink = process.env.LINK_FORGE_DEPENDENCIES_ON_INIT;
    if (shouldLink) {
        d('Linking forge dependencies');
        const packageJson = await (0, read_package_json_1.readRawPackageJson)(dir);
        // TODO(erickzhao): the `--link-folder` argument only works for `yarn`. Since this command is
        // only made for Forge contributors, it isn't a big deal if it doesn't work for other package managers,
        // but we should make it cleaner.
        const linkFolder = node_path_1.default.resolve(__dirname, '..', '..', '..', '..', '..', '..', '.links');
        for (const packageName of Object.keys(packageJson.devDependencies)) {
            if (packageName.startsWith('@electron-forge/')) {
                if (task)
                    task.output = `${pm.executable} link --link-folder ${linkFolder} ${packageName}`;
                await (0, core_utils_1.spawnPackageManager)(pm, ['link', '--link-folder', linkFolder, packageName], {
                    cwd: dir,
                });
            }
        }
    }
    else {
        d('LINK_FORGE_DEPENDENCIES_ON_INIT is falsy. Skipping.');
    }
}
exports.initLink = initLink;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5pdC1saW5rLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vc3JjL2FwaS9pbml0LXNjcmlwdHMvaW5pdC1saW5rLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLDBEQUE2QjtBQUU3QiwyREFBNEU7QUFFNUUsa0RBQTBCO0FBRTFCLG9FQUFrRTtBQUVsRSxNQUFNLENBQUMsR0FBRyxJQUFBLGVBQUssRUFBQywwQkFBMEIsQ0FBQyxDQUFDO0FBRTVDOzs7Ozs7OztHQVFHO0FBQ0ksS0FBSyxVQUFVLFFBQVEsQ0FBSSxFQUFhLEVBQUUsR0FBVyxFQUFFLElBQXdCO0lBQ3BGLE1BQU0sVUFBVSxHQUFHLE9BQU8sQ0FBQyxHQUFHLENBQUMsK0JBQStCLENBQUM7SUFDL0QsSUFBSSxVQUFVLEVBQUUsQ0FBQztRQUNmLENBQUMsQ0FBQyw0QkFBNEIsQ0FBQyxDQUFDO1FBQ2hDLE1BQU0sV0FBVyxHQUFHLE1BQU0sSUFBQSxzQ0FBa0IsRUFBQyxHQUFHLENBQUMsQ0FBQztRQUNsRCw2RkFBNkY7UUFDN0YsdUdBQXVHO1FBQ3ZHLGlDQUFpQztRQUNqQyxNQUFNLFVBQVUsR0FBRyxtQkFBSSxDQUFDLE9BQU8sQ0FBQyxTQUFTLEVBQUUsSUFBSSxFQUFFLElBQUksRUFBRSxJQUFJLEVBQUUsSUFBSSxFQUFFLElBQUksRUFBRSxJQUFJLEVBQUUsUUFBUSxDQUFDLENBQUM7UUFDekYsS0FBSyxNQUFNLFdBQVcsSUFBSSxNQUFNLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxlQUFlLENBQUMsRUFBRSxDQUFDO1lBQ25FLElBQUksV0FBVyxDQUFDLFVBQVUsQ0FBQyxrQkFBa0IsQ0FBQyxFQUFFLENBQUM7Z0JBQy9DLElBQUksSUFBSTtvQkFBRSxJQUFJLENBQUMsTUFBTSxHQUFHLEdBQUcsRUFBRSxDQUFDLFVBQVUsdUJBQXVCLFVBQVUsSUFBSSxXQUFXLEVBQUUsQ0FBQztnQkFDM0YsTUFBTSxJQUFBLGdDQUFtQixFQUFDLEVBQUUsRUFBRSxDQUFDLE1BQU0sRUFBRSxlQUFlLEVBQUUsVUFBVSxFQUFFLFdBQVcsQ0FBQyxFQUFFO29CQUNoRixHQUFHLEVBQUUsR0FBRztpQkFDVCxDQUFDLENBQUM7WUFDTCxDQUFDO1FBQ0gsQ0FBQztJQUNILENBQUM7U0FBTSxDQUFDO1FBQ04sQ0FBQyxDQUFDLHFEQUFxRCxDQUFDLENBQUM7SUFDM0QsQ0FBQztBQUNILENBQUM7QUFwQkQsNEJBb0JDIn0=