"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.findTemplate = void 0;
const debug_1 = __importDefault(require("debug"));
const global_dirs_1 = __importDefault(require("global-dirs"));
const d = (0, debug_1.default)('electron-forge:init:find-template');
var TemplateType;
(function (TemplateType) {
    TemplateType["global"] = "global";
    TemplateType["local"] = "local";
})(TemplateType || (TemplateType = {}));
const findTemplate = async (template) => {
    let templateModulePath;
    const resolveTemplateTypes = [
        [TemplateType.global, `electron-forge-template-${template}`],
        [TemplateType.global, `@electron-forge/template-${template}`],
        [TemplateType.local, `electron-forge-template-${template}`],
        [TemplateType.local, `@electron-forge/template-${template}`],
        [TemplateType.global, template],
        [TemplateType.local, template],
    ];
    let foundTemplate = false;
    for (const [templateType, moduleName] of resolveTemplateTypes) {
        try {
            d(`Trying ${templateType} template: ${moduleName}`);
            if (templateType === TemplateType.global) {
                templateModulePath = require.resolve(moduleName, {
                    paths: [global_dirs_1.default.npm.packages, global_dirs_1.default.yarn.packages],
                });
            }
            else {
                templateModulePath = require.resolve(moduleName);
            }
            foundTemplate = true;
            break;
        }
        catch (err) {
            d(`Error: ${err instanceof Error ? err.message : err}`);
        }
    }
    if (!foundTemplate) {
        throw new Error(`Failed to locate custom template: "${template}".`);
    }
    d(`found template module at: ${templateModulePath}`);
    const templateModule = await Promise.resolve(`${templateModulePath}`).then(s => __importStar(require(s)));
    return templateModule.default ?? templateModule;
};
exports.findTemplate = findTemplate;
//# sourceMappingURL=data:application/json;base64,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