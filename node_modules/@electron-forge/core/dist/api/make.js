"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.listrMake = void 0;
const node_path_1 = __importDefault(require("node:path"));
const get_1 = require("@electron/get");
const core_utils_1 = require("@electron-forge/core-utils");
const tracer_1 = require("@electron-forge/tracer");
const chalk_1 = __importDefault(require("chalk"));
const filenamify_1 = __importDefault(require("filenamify"));
const fs_extra_1 = __importDefault(require("fs-extra"));
const listr2_1 = require("listr2");
const log_symbols_1 = __importDefault(require("log-symbols"));
const forge_config_1 = __importDefault(require("../util/forge-config"));
const hook_1 = require("../util/hook");
const import_search_1 = __importDefault(require("../util/import-search"));
const out_dir_1 = __importDefault(require("../util/out-dir"));
const parse_archs_1 = __importDefault(require("../util/parse-archs"));
const read_package_json_1 = require("../util/read-package-json");
const resolve_dir_1 = __importDefault(require("../util/resolve-dir"));
const package_1 = require("./package");
function generateTargets(forgeConfig, overrideTargets) {
    if (overrideTargets) {
        return overrideTargets.map((target) => {
            if (typeof target === 'string') {
                return forgeConfig.makers.find((maker) => maker.name === target) || { name: target };
            }
            return target;
        });
    }
    return forgeConfig.makers;
}
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function isElectronForgeMaker(target) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return target.__isElectronForgeMaker;
}
const listrMake = (childTrace, { dir: providedDir = process.cwd(), interactive = false, skipPackage = false, arch = (0, get_1.getHostArch)(), platform = process.platform, overrideTargets, outDir, }, receiveMakeResults) => {
    const listrOptions = {
        concurrent: false,
        rendererOptions: {
            collapseSubtasks: false,
            collapseErrors: false,
        },
        silentRendererCondition: !interactive,
        fallbackRendererCondition: Boolean(process.env.DEBUG) || Boolean(process.env.CI),
    };
    const runner = new listr2_1.Listr([
        {
            title: 'Loading configuration',
            task: childTrace({ name: 'load-forge-config', category: '@electron-forge/core' }, async (_, ctx) => {
                const resolvedDir = await (0, resolve_dir_1.default)(providedDir);
                if (!resolvedDir) {
                    throw new Error('Failed to locate startable Electron application');
                }
                ctx.dir = resolvedDir;
                ctx.forgeConfig = await (0, forge_config_1.default)(resolvedDir);
            }),
        },
        {
            title: 'Resolving make targets',
            task: childTrace({ name: 'resolve-make-targets', category: '@electron-forge/core' }, async (_, ctx, task) => {
                const { dir, forgeConfig } = ctx;
                ctx.actualOutDir = outDir || (0, out_dir_1.default)(dir, forgeConfig);
                if (!['darwin', 'win32', 'linux', 'mas'].includes(platform)) {
                    throw new Error(`'${platform}' is an invalid platform. Choices are 'darwin', 'mas', 'win32' or 'linux'.`);
                }
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                const makers = [];
                const possibleMakers = generateTargets(forgeConfig, overrideTargets);
                for (const possibleMaker of possibleMakers) {
                    /* eslint-disable-next-line @typescript-eslint/no-explicit-any */
                    let maker;
                    if (isElectronForgeMaker(possibleMaker)) {
                        maker = possibleMaker;
                        if (!maker.platforms.includes(platform))
                            continue;
                    }
                    else {
                        const resolvableTarget = possibleMaker;
                        // non-false falsy values should be 'true'
                        if (resolvableTarget.enabled === false)
                            continue;
                        if (!resolvableTarget.name) {
                            throw new Error(`The following maker config is missing a maker name: ${JSON.stringify(resolvableTarget)}`);
                        }
                        else if (typeof resolvableTarget.name !== 'string') {
                            throw new Error(`The following maker config has a maker name that is not a string: ${JSON.stringify(resolvableTarget)}`);
                        }
                        const MakerClass = await (0, import_search_1.default)(dir, [resolvableTarget.name]);
                        if (!MakerClass) {
                            throw new Error(`Could not find module with name '${resolvableTarget.name}'. If this is a package from NPM, make sure it's listed in the devDependencies of your package.json. If this is a local module, make sure you have the correct path to its entry point. Try using the DEBUG="electron-forge:require-search" environment variable for more information.`);
                        }
                        maker = new MakerClass(resolvableTarget.config, resolvableTarget.platforms || undefined);
                        if (!maker.platforms.includes(platform))
                            continue;
                    }
                    if (!maker.isSupportedOnCurrentPlatform) {
                        throw new Error([
                            `Maker for target ${maker.name} is incompatible with this version of `,
                            'Electron Forge, please upgrade or contact the maintainer ',
                            "(needs to implement 'isSupportedOnCurrentPlatform)')",
                        ].join(''));
                    }
                    if (!maker.isSupportedOnCurrentPlatform()) {
                        throw new Error(`Cannot make for ${platform} and target ${maker.name}: the maker declared that it cannot run on ${process.platform}.`);
                    }
                    maker.ensureExternalBinariesExist();
                    makers.push(() => maker.clone());
                }
                if (makers.length === 0) {
                    throw new Error(`Could not find any make targets configured for the "${platform}" platform.`);
                }
                ctx.makers = makers;
                task.output = `Making for the following targets: ${chalk_1.default.magenta(`${makers.map((maker) => maker.name).join(', ')}`)}`;
            }),
            rendererOptions: {
                persistentOutput: true,
            },
        },
        {
            title: `Running ${chalk_1.default.yellow('package')} command`,
            task: childTrace({ name: 'package()', category: '@electron-forge/core' }, async (childTrace, ctx, task) => {
                if (!skipPackage) {
                    return (0, tracer_1.delayTraceTillSignal)(childTrace, (0, package_1.listrPackage)(childTrace, {
                        dir: ctx.dir,
                        interactive,
                        arch,
                        outDir: ctx.actualOutDir,
                        platform,
                    }), 'run');
                }
                else {
                    task.output = chalk_1.default.yellow(`${log_symbols_1.default.warning} Skipping could result in an out of date build`);
                    task.skip();
                }
            }),
            rendererOptions: {
                persistentOutput: true,
            },
        },
        {
            title: `Running ${chalk_1.default.yellow('preMake')} hook`,
            task: childTrace({ name: 'run-preMake-hook', category: '@electron-forge/core' }, async (childTrace, ctx, task) => {
                return (0, tracer_1.delayTraceTillSignal)(childTrace, task.newListr(await (0, hook_1.getHookListrTasks)(childTrace, ctx.forgeConfig, 'preMake')), 'run');
            }),
        },
        {
            title: 'Making distributables',
            task: childTrace({ name: 'make-distributables', category: '@electron-forge/core' }, async (childTrace, ctx, task) => {
                const { actualOutDir, dir, forgeConfig, makers } = ctx;
                const packageJSON = await (0, read_package_json_1.readMutatedPackageJson)(dir, forgeConfig);
                const appName = (0, filenamify_1.default)(forgeConfig.packagerConfig.name || packageJSON.productName || packageJSON.name, { replacement: '-' });
                const outputs = [];
                ctx.outputs = outputs;
                const subRunner = task.newListr([], {
                    ...listrOptions,
                    concurrent: true,
                    rendererOptions: {
                        collapseSubtasks: false,
                        collapseErrors: false,
                    },
                });
                for (const targetArch of (0, parse_archs_1.default)(platform, arch, await (0, core_utils_1.getElectronVersion)(dir, packageJSON))) {
                    const packageDir = node_path_1.default.resolve(actualOutDir, `${appName}-${platform}-${targetArch}`);
                    if (!(await fs_extra_1.default.pathExists(packageDir))) {
                        throw new Error(`Couldn't find packaged app at: ${packageDir}`);
                    }
                    for (const maker of makers) {
                        const uniqMaker = maker();
                        subRunner.add({
                            title: `Making a ${chalk_1.default.magenta(uniqMaker.name)} distributable for ${chalk_1.default.cyan(`${platform}/${targetArch}`)}`,
                            task: childTrace({ name: `make-${maker.name}`, category: '@electron-forge/core', newRoot: true }, async () => {
                                try {
                                    await Promise.resolve(uniqMaker.prepareConfig(targetArch));
                                    const artifacts = await uniqMaker.make({
                                        appName,
                                        forgeConfig,
                                        packageJSON,
                                        targetArch,
                                        dir: packageDir,
                                        makeDir: node_path_1.default.resolve(actualOutDir, 'make'),
                                        targetPlatform: platform,
                                    });
                                    outputs.push({
                                        artifacts,
                                        packageJSON,
                                        platform,
                                        arch: targetArch,
                                    });
                                }
                                catch (err) {
                                    if (err instanceof Error) {
                                        throw err;
                                    }
                                    else if (typeof err === 'string') {
                                        throw new Error(err);
                                    }
                                    else {
                                        throw new Error(`An unknown error occurred while making for target: ${uniqMaker.name}`);
                                    }
                                }
                            }),
                            rendererOptions: {
                                timer: { ...listr2_1.PRESET_TIMER },
                            },
                        });
                    }
                }
                return (0, tracer_1.delayTraceTillSignal)(childTrace, subRunner, 'run');
            }),
        },
        {
            title: `Running ${chalk_1.default.yellow('postMake')} hook`,
            task: childTrace({ name: 'run-postMake-hook', category: '@electron-forge/core' }, async (_, ctx, task) => {
                // If the postMake hooks modifies the locations / names of the outputs it must return
                // the new locations so that the publish step knows where to look
                const originalOutputs = JSON.stringify(ctx.outputs);
                ctx.outputs = await (0, hook_1.runMutatingHook)(ctx.forgeConfig, 'postMake', ctx.outputs);
                let outputLocations = [node_path_1.default.resolve(ctx.actualOutDir, 'make')];
                if (originalOutputs !== JSON.stringify(ctx.outputs)) {
                    const newDirs = new Set();
                    const artifactPaths = [];
                    for (const result of ctx.outputs) {
                        for (const artifact of result.artifacts) {
                            newDirs.add(node_path_1.default.dirname(artifact));
                            artifactPaths.push(artifact);
                        }
                    }
                    if (newDirs.size <= ctx.outputs.length) {
                        outputLocations = [...newDirs];
                    }
                    else {
                        outputLocations = artifactPaths;
                    }
                }
                receiveMakeResults?.(ctx.outputs);
                task.output = `Artifacts available at: ${chalk_1.default.green(outputLocations.join(', '))}`;
            }),
            rendererOptions: {
                persistentOutput: true,
            },
        },
    ], {
        ...listrOptions,
        ctx: {},
    });
    return runner;
};
exports.listrMake = listrMake;
exports.default = (0, tracer_1.autoTrace)({ name: 'make()', category: '@electron-forge/core' }, async (childTrace, opts) => {
    const runner = (0, exports.listrMake)(childTrace, opts);
    await runner.run();
    return runner.ctx.outputs;
});
//# sourceMappingURL=data:application/json;base64,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