{"name": "@electron-forge/core", "version": "7.7.0", "description": "A complete tool for building modern Electron applications", "repository": "https://github.com/electron/forge", "main": "dist/api/index.js", "typings": "dist/api/index.d.ts", "author": "<PERSON>", "license": "MIT", "devDependencies": {"@electron-forge/maker-appx": "7.7.0", "@electron-forge/maker-deb": "7.7.0", "@electron-forge/maker-dmg": "7.7.0", "@electron-forge/maker-flatpak": "7.7.0", "@electron-forge/maker-rpm": "7.7.0", "@electron-forge/maker-snap": "7.7.0", "@electron-forge/maker-squirrel": "7.7.0", "@electron-forge/maker-wix": "7.7.0", "@electron-forge/maker-zip": "7.7.0", "@electron-forge/template-fixture": "file:./spec/fixture/electron-forge-template-fixture", "@electron-forge/test-utils": "7.7.0", "@types/interpret": "^1.1.1", "@types/progress": "^2.0.5", "@types/rechoir": "^0.6.1", "electron-forge-template-fixture-two": "file:./spec/fixture/electron-forge-template-fixture", "electron-installer-common": "^0.10.2", "vitest": "^3.0.3", "yaml-hook": "^1.0.0"}, "dependencies": {"@electron-forge/core-utils": "7.7.0", "@electron-forge/maker-base": "7.7.0", "@electron-forge/plugin-base": "7.7.0", "@electron-forge/publisher-base": "7.7.0", "@electron-forge/shared-types": "7.7.0", "@electron-forge/template-base": "7.7.0", "@electron-forge/template-vite": "7.7.0", "@electron-forge/template-vite-typescript": "7.7.0", "@electron-forge/template-webpack": "7.7.0", "@electron-forge/template-webpack-typescript": "7.7.0", "@electron-forge/tracer": "7.7.0", "@electron/get": "^3.0.0", "@electron/packager": "^18.3.5", "@electron/rebuild": "^3.7.0", "@malept/cross-spawn-promise": "^2.0.0", "chalk": "^4.0.0", "debug": "^4.3.1", "fast-glob": "^3.2.7", "filenamify": "^4.1.0", "find-up": "^5.0.0", "fs-extra": "^10.0.0", "global-dirs": "^3.0.0", "got": "^11.8.5", "interpret": "^3.1.1", "listr2": "^7.0.2", "lodash": "^4.17.20", "log-symbols": "^4.0.0", "node-fetch": "^2.6.7", "rechoir": "^0.8.0", "semver": "^7.2.1", "source-map-support": "^0.5.13", "sudo-prompt": "^9.1.1", "username": "^5.1.0"}, "engines": {"node": ">= 16.4.0"}, "funding": [{"type": "individual", "url": "https://github.com/sponsors/malept"}, {"type": "tidelift", "url": "https://tidelift.com/subscription/pkg/npm-.electron-forge-core?utm_medium=referral&utm_source=npm_fund"}], "publishConfig": {"access": "public"}, "files": ["dist", "src", "helper"], "gitHead": "6a88c47b5916a39ee9f993d98d420c6c857de54c"}