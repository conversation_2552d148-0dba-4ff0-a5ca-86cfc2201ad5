"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MakerBase = void 0;
const node_path_1 = __importDefault(require("node:path"));
const fs_extra_1 = __importDefault(require("fs-extra"));
const which_1 = __importDefault(require("which"));
class Maker {
    /**
     * @param configOrConfigFetcher - Either a configuration object for this maker or a simple method that returns such a configuration for a given target architecture
     * @param platformsToMakeOn - If you want this maker to run on platforms different from `defaultPlatforms` you can provide those platforms here
     */
    constructor(configOrConfigFetcher = {}, platformsToMakeOn) {
        this.configOrConfigFetcher = configOrConfigFetcher;
        this.platformsToMakeOn = platformsToMakeOn;
        this.requiredExternalBinaries = [];
        Object.defineProperty(this, '__isElectronForgeMaker', {
            value: true,
            enumerable: false,
            configurable: false,
        });
    }
    get platforms() {
        if (this.platformsToMakeOn)
            return this.platformsToMakeOn;
        return this.defaultPlatforms;
    }
    // TODO: Remove this, it is an eye-sore and is a nasty hack to provide forge
    //       v5 style functionality in the new API
    async prepareConfig(targetArch) {
        if (typeof this.configOrConfigFetcher === 'function') {
            this.config = await Promise.resolve(this.configOrConfigFetcher(targetArch));
        }
        else {
            this.config = this.configOrConfigFetcher;
        }
    }
    /**
     * Makers must implement this method and return true or false indicating whether
     * this maker can be run on the current platform.  Normally this is just a process.platform
     * check but it can be a deeper check for dependencies like fake-root or other
     * required external build tools.
     *
     * If the issue is a missing dependency you should log out a HELPFUL error message
     * telling the developer exactly what is missing and if possible how to get it.
     */
    isSupportedOnCurrentPlatform() {
        if (this.isSupportedOnCurrentPlatform === Maker.prototype.isSupportedOnCurrentPlatform) {
            throw new Error(`Maker ${this.name} did not implement the isSupportedOnCurrentPlatform method`);
        }
        return true;
    }
    clone() {
        const MakerClass = this.constructor;
        return new MakerClass(this.configOrConfigFetcher, this.platformsToMakeOn);
    }
    /**
     * Makers must implement this method and return an array of absolute paths
     * to the artifacts generated by your maker
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async make(opts) {
        if (this.make === Maker.prototype.make) {
            throw new Error(`Maker ${this.name} did not implement the make method`);
        }
        return [];
    }
    /**
     * Helpers
     */
    /**
     * Ensures the directory exists and is forced to be empty.
     *
     * I.e. If the directory already exists it is deleted and recreated, this
     * is a destructive operation
     */
    async ensureDirectory(dir) {
        if (await fs_extra_1.default.pathExists(dir)) {
            await fs_extra_1.default.remove(dir);
        }
        return fs_extra_1.default.mkdirs(dir);
    }
    /**
     * Ensures the path to the file exists and the file does not exist
     *
     * I.e. If the file already exists it is deleted and the path created
     */
    async ensureFile(file) {
        if (await fs_extra_1.default.pathExists(file)) {
            await fs_extra_1.default.remove(file);
        }
        await fs_extra_1.default.mkdirs(node_path_1.default.dirname(file));
    }
    /**
     * Checks if the specified binaries exist, which are required for the maker to be used.
     */
    externalBinariesExist() {
        return this.requiredExternalBinaries.every((binary) => which_1.default.sync(binary, { nothrow: true }) !== null);
    }
    /**
     * Throws an error if any of the binaries don't exist.
     */
    ensureExternalBinariesExist() {
        if (!this.externalBinariesExist()) {
            throw new Error(`Cannot make for ${this.name}, the following external binaries need to be installed: ${this.requiredExternalBinaries.join(', ')}`);
        }
    }
    /**
     * Checks if the given module is installed, used for testing if optional dependencies
     * are installed or not
     */
    isInstalled(module) {
        try {
            require(module);
            return true;
        }
        catch {
            // Package doesn't exist -- must not be installable on this platform
            return false;
        }
    }
    /**
     * Normalize the given semver-formatted version to a 4-part dot delimited version number without
     * prerelease information for use in Windows apps.
     */
    normalizeWindowsVersion(version) {
        const noPrerelease = version.replace(/[-+].*/, '');
        return `${noPrerelease}.0`;
    }
}
exports.default = Maker;
exports.MakerBase = Maker;
//# sourceMappingURL=data:application/json;base64,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