import { ForgeArch, ForgePlatform, IForgeMaker, ResolvedForgeConfig } from '@electron-forge/shared-types';
export type EmptyConfig = Record<string, never>;
export interface MakerOptions {
    /**
     * The directory containing the packaged Electron application
     */
    dir: string;
    /**
     * The directory you should put all your artifacts in (potentially in sub folders)
     * NOTE: this directory is not guarunteed to already exist
     */
    makeDir: string;
    /**
     * The resolved human friendly name of the project
     */
    appName: string;
    /**
     * The target platform you should make for
     */
    targetPlatform: ForgePlatform;
    /**
     * The target architecture you should make for
     */
    targetArch: ForgeArch;
    /**
     * Fully resolved forge configuration, you shouldn't really need this
     */
    forgeConfig: ResolvedForgeConfig;
    /**
     * The application's package.json file
     */
    packageJSON: any;
}
export default abstract class Maker<C> implements IForgeMaker {
    private configOrConfigFetcher;
    protected platformsToMakeOn?: string[] | undefined;
    config: C;
    abstract name: string;
    abstract defaultPlatforms: ForgePlatform[];
    requiredExternalBinaries: string[];
    /** @internal */
    __isElectronForgeMaker: true;
    /**
     * @param configOrConfigFetcher - Either a configuration object for this maker or a simple method that returns such a configuration for a given target architecture
     * @param platformsToMakeOn - If you want this maker to run on platforms different from `defaultPlatforms` you can provide those platforms here
     */
    constructor(configOrConfigFetcher?: C | ((arch: ForgeArch) => C), platformsToMakeOn?: string[] | undefined);
    get platforms(): ForgePlatform[];
    prepareConfig(targetArch: ForgeArch): Promise<void>;
    /**
     * Makers must implement this method and return true or false indicating whether
     * this maker can be run on the current platform.  Normally this is just a process.platform
     * check but it can be a deeper check for dependencies like fake-root or other
     * required external build tools.
     *
     * If the issue is a missing dependency you should log out a HELPFUL error message
     * telling the developer exactly what is missing and if possible how to get it.
     */
    isSupportedOnCurrentPlatform(): boolean;
    clone(): Maker<C>;
    /**
     * Makers must implement this method and return an array of absolute paths
     * to the artifacts generated by your maker
     */
    make(opts: MakerOptions): Promise<string[]>;
    /**
     * Helpers
     */
    /**
     * Ensures the directory exists and is forced to be empty.
     *
     * I.e. If the directory already exists it is deleted and recreated, this
     * is a destructive operation
     */
    ensureDirectory(dir: string): Promise<void>;
    /**
     * Ensures the path to the file exists and the file does not exist
     *
     * I.e. If the file already exists it is deleted and the path created
     */
    ensureFile(file: string): Promise<void>;
    /**
     * Checks if the specified binaries exist, which are required for the maker to be used.
     */
    externalBinariesExist(): boolean;
    /**
     * Throws an error if any of the binaries don't exist.
     */
    ensureExternalBinariesExist(): void;
    /**
     * Checks if the given module is installed, used for testing if optional dependencies
     * are installed or not
     */
    isInstalled(module: string): boolean;
    /**
     * Normalize the given semver-formatted version to a 4-part dot delimited version number without
     * prerelease information for use in Windows apps.
     */
    normalizeWindowsVersion(version: string): string;
}
export { Maker as MakerBase };
//# sourceMappingURL=Maker.d.ts.map