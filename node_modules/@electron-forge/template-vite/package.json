{"name": "@electron-forge/template-vite", "version": "7.7.0", "description": "Vite template for Electron Forge, gets you started with Vite really quickly", "repository": {"type": "git", "url": "https://github.com/electron/forge", "directory": "packages/template/vite"}, "author": "caoxie<PERSON><PERSON>", "license": "MIT", "main": "dist/ViteTemplate.js", "typings": "dist/ViteTemplate.d.ts", "engines": {"node": ">= 16.4.0"}, "dependencies": {"@electron-forge/shared-types": "7.7.0", "@electron-forge/template-base": "7.7.0", "fs-extra": "^10.0.0"}, "devDependencies": {"@electron-forge/test-utils": "7.7.0", "listr2": "^7.0.2", "vitest": "^3.0.3"}, "publishConfig": {"access": "public"}, "files": ["dist", "src", "tmpl"], "gitHead": "6a88c47b5916a39ee9f993d98d420c6c857de54c"}