"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const node_path_1 = __importDefault(require("node:path"));
const template_base_1 = require("@electron-forge/template-base");
const fs_extra_1 = __importDefault(require("fs-extra"));
class ViteTemplate extends template_base_1.BaseTemplate {
    constructor() {
        super(...arguments);
        this.templateDir = node_path_1.default.resolve(__dirname, '..', 'tmpl');
    }
    async initializeTemplate(directory, options) {
        const superTasks = await super.initializeTemplate(directory, options);
        return [
            ...superTasks,
            {
                title: 'Setting up Forge configuration',
                task: async () => {
                    await this.copyTemplateFile(directory, 'forge.config.js');
                },
            },
            {
                title: 'Setting up Vite configuration',
                task: async () => {
                    await this.copyTemplateFile(directory, 'vite.main.config.mjs');
                    await this.copyTemplateFile(directory, 'vite.preload.config.mjs');
                    await this.copyTemplateFile(directory, 'vite.renderer.config.mjs');
                    await this.copyTemplateFile(node_path_1.default.join(directory, 'src'), 'renderer.js');
                    await this.copyTemplateFile(node_path_1.default.join(directory, 'src'), 'preload.js');
                    await this.copyTemplateFile(node_path_1.default.join(directory, 'src'), 'index.js');
                    await this.updateFileByLine(node_path_1.default.resolve(directory, 'src', 'index.js'), (line) => {
                        if (line.includes('mainWindow.loadFile'))
                            return `  if (MAIN_WINDOW_VITE_DEV_SERVER_URL) {
    mainWindow.loadURL(MAIN_WINDOW_VITE_DEV_SERVER_URL);
  } else {
    mainWindow.loadFile(path.join(__dirname, \`../renderer/\${MAIN_WINDOW_VITE_NAME}/index.html\`));
  }`;
                        return line;
                    }, node_path_1.default.resolve(directory, 'src', 'main.js'));
                    // TODO: Compatible with any path entry.
                    // Vite uses index.html under the root path as the entry point.
                    fs_extra_1.default.moveSync(node_path_1.default.join(directory, 'src', 'index.html'), node_path_1.default.join(directory, 'index.html'), { overwrite: options.force });
                    await this.updateFileByLine(node_path_1.default.join(directory, 'index.html'), (line) => {
                        if (line.includes('link rel="stylesheet"'))
                            return '';
                        if (line.includes('</body>'))
                            return '    <script type="module" src="/src/renderer.js"></script>\n  </body>';
                        return line;
                    });
                    // update package.json entry point
                    const pjPath = node_path_1.default.resolve(directory, 'package.json');
                    const currentPJ = await fs_extra_1.default.readJson(pjPath);
                    currentPJ.main = '.vite/build/main.js';
                    await fs_extra_1.default.writeJson(pjPath, currentPJ, {
                        spaces: 2,
                    });
                },
            },
        ];
    }
}
exports.default = new ViteTemplate();
//# sourceMappingURL=data:application/json;base64,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