import { ForgeHookFn, ForgeListrTask, ForgeMultiHookMap, IForgePlugin, ResolvedForgeConfig, StartOptions, StartResult } from '@electron-forge/shared-types';
export { StartOptions };
export default abstract class Plugin<C> implements IForgePlugin {
    config: C;
    abstract name: string;
    /** @internal */
    __isElectronForgePlugin: true;
    /** @internal */
    _resolvedHooks: ForgeMultiHookMap;
    constructor(config: C);
    init(_dir: string, _config: ResolvedForgeConfig): void;
    getHooks(): ForgeMultiHookMap;
    startLogic(_startOpts: StartOptions): Promise<StartResult>;
}
export declare const namedHookWithTaskFn: <Hook extends keyof import("@electron-forge/shared-types").ForgeSimpleHookSignatures | keyof import("@electron-forge/shared-types").ForgeMutatingHookSignatures>(hookFn: <Ctx = never>(task: ForgeListrTask<Ctx> | null, ...args: Parameters<ForgeHookFn<Hook>>) => ReturnType<ForgeHookFn<Hook>>, name: string) => ForgeHookFn<Hook>;
export { Plugin as PluginBase };
//# sourceMappingURL=Plugin.d.ts.map