{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAElD,OAAO,EAAE,UAAU,EAAE,OAAO,IAAI,uBAAuB,EAAE,cAAc,EAAE,MAAM,oBAAoB,CAAC;AACpG,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AACnD,OAAO,EAAE,SAAS,EAAE,MAAM,wBAAwB,CAAC;AACnD,OAAO,EACL,qBAAqB,EACrB,oBAAoB,EACpB,yBAAyB,EACzB,mBAAmB,EACnB,wBAAwB,EACxB,SAAS,EACT,gBAAgB,EACjB,MAAM,QAAQ,CAAC;AAEhB,MAAM,MAAM,iBAAiB,CAAC,CAAC,IAAI,qBAAqB,CAAC,CAAC,EAAE,yBAAyB,EAAE,wBAAwB,CAAC,CAAC;AACjH,MAAM,MAAM,cAAc,CAAC,CAAC,IAAI,gBAAgB,CAAC,CAAC,EAAE,oBAAoB,EAAE,oBAAoB,GAAG,mBAAmB,CAAC,CAAC;AACtH,MAAM,MAAM,gBAAgB,CAAC,GAAG,GAAG,GAAG,IAAI,SAAS,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC,MAAM,CAAC,CAAC;AACvF,MAAM,MAAM,eAAe,GAAG,YAAY,GAAG;IAAE,SAAS,EAAE,OAAO,CAAA;CAAE,CAAC;AAEpE,MAAM,MAAM,aAAa,GAAG,cAAc,CAAC;AAC3C,MAAM,MAAM,SAAS,GAAG,UAAU,CAAC;AACnC,MAAM,MAAM,oBAAoB,GAAG,yBAAyB,GAAG,eAAe,CAAC;AAC/E,MAAM,MAAM,gBAAgB,GAAG,qBAAqB,GAAG,WAAW,CAAC;AACnE,MAAM,MAAM,iBAAiB,GAAG,sBAAsB,GAAG,YAAY,CAAC;AAEtE,MAAM,WAAW,yBAAyB;IACxC,cAAc,EAAE,CAAC,QAAQ,EAAE,aAAa,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;IAC9D,QAAQ,EAAE,EAAE,CAAC;IACb,SAAS,EAAE,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC;IACzC,UAAU,EAAE,CAAC,QAAQ,EAAE,aAAa,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;IAC1D,gBAAgB,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;IACzG,iBAAiB,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;IAC1G,mBAAmB,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;IAC5G,WAAW,EAAE;QACX,aAAa,EAAE;YACb,QAAQ,EAAE,aAAa,CAAC;YACxB,IAAI,EAAE,SAAS,CAAC;YAChB,WAAW,EAAE,MAAM,EAAE,CAAC;SACvB;KACF,CAAC;IACF,OAAO,EAAE,EAAE,CAAC;CACb;AAED,MAAM,WAAW,2BAA2B;IAC1C,QAAQ,EAAE,CAAC,WAAW,EAAE,eAAe,EAAE,CAAC,CAAC;IAC3C,kBAAkB,EAAE,CAAC,aAAa,EAAE,mBAAmB,CAAC,CAAC;IAEzD,eAAe,EAAE,CAAC,WAAW,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;CACrD;AAED,MAAM,MAAM,aAAa,GAAG,MAAM,CAAC,yBAAyB,GAAG,2BAA2B,CAAC,CAAC;AAC5F,MAAM,MAAM,iBAAiB,CAAC,IAAI,SAAS,MAAM,yBAAyB,IAAI,CAC5E,WAAW,EAAE,mBAAmB,EAChC,GAAG,IAAI,EAAE,yBAAyB,CAAC,IAAI,CAAC,KACrC,OAAO,CAAC,IAAI,CAAC,CAAC;AACnB,MAAM,MAAM,mBAAmB,CAAC,IAAI,SAAS,MAAM,2BAA2B,IAAI,CAChF,WAAW,EAAE,mBAAmB,EAChC,GAAG,IAAI,EAAE,2BAA2B,CAAC,IAAI,CAAC,KACvC,OAAO,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AAC1D,MAAM,MAAM,WAAW,CAAC,IAAI,SAAS,aAAa,IAAI,IAAI,SAAS,MAAM,yBAAyB,GAC9F,iBAAiB,CAAC,IAAI,CAAC,GACvB,IAAI,SAAS,MAAM,2BAA2B,GAC9C,mBAAmB,CAAC,IAAI,CAAC,GACzB,KAAK,CAAC;AACV,MAAM,MAAM,YAAY,GAAG;KACxB,CAAC,IAAI,aAAa,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC;CACtC,CAAC;AACF,MAAM,MAAM,iBAAiB,GAAG;KAC7B,CAAC,IAAI,aAAa,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE;CACzD,CAAC;AAEF,MAAM,WAAW,qBAAqB;IACpC,WAAW,CAAC,IAAI,SAAS,MAAM,yBAAyB,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,yBAAyB,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IACpI,iBAAiB,CAAC,IAAI,SAAS,MAAM,yBAAyB,EAC5D,UAAU,EAAE,OAAO,SAAS,EAC5B,QAAQ,EAAE,IAAI,EACd,QAAQ,EAAE,yBAAyB,CAAC,IAAI,CAAC,GACxC,OAAO,CAAC,wBAAwB,EAAE,CAAC,CAAC;IACvC,mBAAmB,CAAC,IAAI,SAAS,MAAM,2BAA2B,EAChE,QAAQ,EAAE,IAAI,EACd,IAAI,EAAE,2BAA2B,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GACzC,OAAO,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjD,kBAAkB,CAAC,IAAI,EAAE,YAAY,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC;CAC9D;AAGD,MAAM,MAAM,mBAAmB,GAAG,IAAI,CAAC,cAAc,EAAE,WAAW,GAAG,iBAAiB,GAAG,MAAM,CAAC,CAAC;AACjG,MAAM,MAAM,oBAAoB,GAAG,IAAI,CAAC,uBAAuB,EAAE,KAAK,GAAG,MAAM,GAAG,UAAU,GAAG,KAAK,GAAG,iBAAiB,CAAC,CAAC;AAC1H,MAAM,WAAW,mBAAmB;IAClC;;;;;OAKG;IACH,eAAe,CAAC,EAAE,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC,CAAC;IAC1C;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,KAAK,CAAC,EAAE,YAAY,CAAC;IACrB;;OAEG;IACH,eAAe,EAAE,qBAAqB,CAAC;IACvC;;OAEG;IACH,OAAO,EAAE,iBAAiB,EAAE,CAAC;IAC7B,aAAa,EAAE,mBAAmB,CAAC;IACnC,cAAc,EAAE,oBAAoB,CAAC;IACrC,MAAM,EAAE,gBAAgB,EAAE,CAAC;IAC3B,UAAU,EAAE,oBAAoB,EAAE,CAAC;CACpC;AACD,MAAM,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,CAAC,CAAC;AAChF,MAAM,WAAW,eAAe;IAC9B;;OAEG;IACH,SAAS,EAAE,MAAM,EAAE,CAAC;IACpB;;OAEG;IACH,WAAW,EAAE,GAAG,CAAC;IACjB;;OAEG;IACH,QAAQ,EAAE,aAAa,CAAC;IACxB;;OAEG;IACH,IAAI,EAAE,SAAS,CAAC;CACjB;AAED,MAAM,WAAW,sBAAsB;IACrC,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,CAAC,EAAE,GAAG,CAAC;CACd;AAED,MAAM,WAAW,YAAY;IAC3B,gBAAgB;IAChB,uBAAuB,EAAE,OAAO,CAAC;IACjC,IAAI,EAAE,MAAM,CAAC;IAEb,IAAI,CAAC,GAAG,EAAE,MAAM,EAAE,WAAW,EAAE,mBAAmB,GAAG,IAAI,CAAC;IAC1D,QAAQ,CAAC,IAAI,iBAAiB,CAAC;IAC/B,UAAU,CAAC,CAAC,IAAI,EAAE,YAAY,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC;CACvD;AAED,MAAM,WAAW,qBAAqB;IACpC,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,GAAG,CAAC;IACZ,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,SAAS,CAAC,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC;CACpC;AAED,MAAM,WAAW,WAAW;IAC1B,gBAAgB;IAChB,sBAAsB,EAAE,OAAO,CAAC;IAChC,QAAQ,CAAC,SAAS,CAAC,EAAE,aAAa,EAAE,CAAC;CACtC;AAED,MAAM,WAAW,yBAAyB;IACxC,IAAI,EAAE,MAAM,CAAC;IACb,SAAS,CAAC,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC;IACnC,MAAM,CAAC,EAAE,GAAG,CAAC;CACd;AAED,MAAM,WAAW,eAAe;IAC9B,gBAAgB;IAChB,0BAA0B,EAAE,OAAO,CAAC;IACpC,QAAQ,CAAC,SAAS,CAAC,EAAE,aAAa,EAAE,CAAC;CACtC;AAED,MAAM,WAAW,YAAY;IAC3B;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;IACb;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB;;OAEG;IACH,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB;;OAEG;IACH,IAAI,CAAC,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC,EAAE,CAAC;IAC3B;;OAEG;IACH,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB;;OAEG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB;;;OAGG;IACH,UAAU,CAAC,EAAE,OAAO,CAAC;CACtB;AAED,MAAM,MAAM,gBAAgB,GAAG,eAAe,GAAG,MAAM,GAAG,MAAM,EAAE,GAAG,KAAK,CAAC;AAC3E,MAAM,MAAM,WAAW,GAAG,gBAAgB,GAAG;IAAE,KAAK,EAAE,wBAAwB,EAAE,CAAC;IAAC,MAAM,EAAE,gBAAgB,CAAA;CAAE,CAAC;AAE7G,MAAM,WAAW,mBAAmB;IAClC,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB,KAAK,CAAC,EAAE,OAAO,CAAC;CACjB;AAGD,MAAM,MAAM,wBAAwB,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;AACxD,OAAO,EAAE,SAAS,EAAE,CAAC;AAErB,MAAM,WAAW,aAAa;IAC5B,oBAAoB,CAAC,EAAE,MAAM,CAAC;IAC9B,YAAY,CAAC,EAAE,MAAM,EAAE,CAAC;IACxB,eAAe,CAAC,EAAE,MAAM,EAAE,CAAC;IAC3B,kBAAkB,CAAC,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,mBAAmB,KAAK,OAAO,CAAC,IAAI,GAAG,wBAAwB,EAAE,CAAC,CAAC;CAChH;AAED,MAAM,MAAM,aAAa,GACrB,SAAS,GACT,MAAM,GACN;IACE,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,GAAG,CAAC,EAAE,MAAM,CAAC;CACd,CAAC"}