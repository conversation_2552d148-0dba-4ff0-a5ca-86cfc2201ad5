{"name": "@electron-forge/shared-types", "version": "7.7.0", "description": "Shared types across Electron Forge", "repository": "https://github.com/electron/forge", "author": "<PERSON>", "license": "MIT", "main": "dist/index.js", "typings": "dist/index.d.ts", "dependencies": {"@electron-forge/tracer": "7.7.0", "@electron/packager": "^18.3.5", "@electron/rebuild": "^3.7.0", "listr2": "^7.0.2"}, "engines": {"node": ">= 16.4.0"}, "publishConfig": {"access": "public"}, "files": ["dist", "src"], "gitHead": "6a88c47b5916a39ee9f993d98d420c6c857de54c"}