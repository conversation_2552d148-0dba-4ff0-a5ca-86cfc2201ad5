{"name": "@electron-forge/publisher-base", "version": "7.7.0", "description": "Base publisher for Electron Forge", "repository": "https://github.com/electron/forge", "author": "<PERSON>", "license": "MIT", "main": "dist/Publisher.js", "typings": "dist/Publisher.d.ts", "dependencies": {"@electron-forge/shared-types": "7.7.0"}, "devDependencies": {"vitest": "^3.0.3"}, "engines": {"node": ">= 16.4.0"}, "publishConfig": {"access": "public"}, "files": ["dist", "src"], "gitHead": "6a88c47b5916a39ee9f993d98d420c6c857de54c"}