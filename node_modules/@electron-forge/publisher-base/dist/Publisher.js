"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PublisherBase = void 0;
class Publisher {
    /**
     * @param config - A configuration object for this publisher
     * @param platformsToPublishOn - If you want this maker to run on platforms different from `defaultPlatforms` you can provide those platforms here
     */
    constructor(config, platformsToPublishOn) {
        this.config = config;
        this.platformsToPublishOn = platformsToPublishOn;
        this.config = config;
        Object.defineProperty(this, '__isElectronForgePublisher', {
            value: true,
            enumerable: false,
            configurable: false,
        });
    }
    get platforms() {
        if (this.platformsToPublishOn)
            return this.platformsToPublishOn;
        if (this.defaultPlatforms)
            return this.defaultPlatforms;
        return ['win32', 'linux', 'darwin', 'mas'];
    }
    /**
     * Publishers must implement this method to publish the artifacts returned from
     * make calls.  If any errors occur you must throw them, failing silently or simply
     * logging will not propagate issues up to forge.
     *
     * Please note for a given version publish will be called multiple times, once
     * for each set of "platform" and "arch".  This means if you are publishing
     * darwin and win32 artifacts to somewhere like GitHub on the first publish call
     * you will have to create the version on GitHub and the second call will just
     * be appending files to the existing version.
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async publish(opts) {
        throw new Error(`Publisher ${this.name} did not implement the publish method`);
    }
}
exports.default = Publisher;
exports.PublisherBase = Publisher;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiUHVibGlzaGVyLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vc3JjL1B1Ymxpc2hlci50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7QUF5QkEsTUFBOEIsU0FBUztJQVFyQzs7O09BR0c7SUFDSCxZQUFtQixNQUFTLEVBQVksb0JBQXNDO1FBQTNELFdBQU0sR0FBTixNQUFNLENBQUc7UUFBWSx5QkFBb0IsR0FBcEIsb0JBQW9CLENBQWtCO1FBQzVFLElBQUksQ0FBQyxNQUFNLEdBQUcsTUFBTSxDQUFDO1FBQ3JCLE1BQU0sQ0FBQyxjQUFjLENBQUMsSUFBSSxFQUFFLDRCQUE0QixFQUFFO1lBQ3hELEtBQUssRUFBRSxJQUFJO1lBQ1gsVUFBVSxFQUFFLEtBQUs7WUFDakIsWUFBWSxFQUFFLEtBQUs7U0FDcEIsQ0FBQyxDQUFDO0lBQ0wsQ0FBQztJQUVELElBQUksU0FBUztRQUNYLElBQUksSUFBSSxDQUFDLG9CQUFvQjtZQUFFLE9BQU8sSUFBSSxDQUFDLG9CQUFvQixDQUFDO1FBQ2hFLElBQUksSUFBSSxDQUFDLGdCQUFnQjtZQUFFLE9BQU8sSUFBSSxDQUFDLGdCQUFnQixDQUFDO1FBQ3hELE9BQU8sQ0FBQyxPQUFPLEVBQUUsT0FBTyxFQUFFLFFBQVEsRUFBRSxLQUFLLENBQUMsQ0FBQztJQUM3QyxDQUFDO0lBRUQ7Ozs7Ozs7Ozs7T0FVRztJQUNILDZEQUE2RDtJQUM3RCxLQUFLLENBQUMsT0FBTyxDQUFDLElBQXNCO1FBQ2xDLE1BQU0sSUFBSSxLQUFLLENBQUMsYUFBYSxJQUFJLENBQUMsSUFBSSx1Q0FBdUMsQ0FBQyxDQUFDO0lBQ2pGLENBQUM7Q0FDRjtBQTFDRCw0QkEwQ0M7QUFFcUIsa0NBQWEifQ==