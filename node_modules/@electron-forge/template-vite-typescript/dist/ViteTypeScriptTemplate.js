"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const node_path_1 = __importDefault(require("node:path"));
const template_base_1 = require("@electron-forge/template-base");
const fs_extra_1 = __importDefault(require("fs-extra"));
class ViteTypeScriptTemplate extends template_base_1.BaseTemplate {
    constructor() {
        super(...arguments);
        this.templateDir = node_path_1.default.resolve(__dirname, '..', 'tmpl');
    }
    async initializeTemplate(directory, options) {
        const superTasks = await super.initializeTemplate(directory, options);
        return [
            ...superTasks,
            {
                title: 'Setting up Forge configuration',
                task: async () => {
                    await this.copyTemplateFile(directory, 'forge.env.d.ts');
                    await this.copyTemplateFile(directory, 'forge.config.ts');
                    await fs_extra_1.default.remove(node_path_1.default.resolve(directory, 'forge.config.js'));
                },
            },
            {
                title: 'Preparing TypeScript files and configuration',
                task: async () => {
                    const filePath = (fileName) => node_path_1.default.join(directory, 'src', fileName);
                    // Copy Vite files
                    await this.copyTemplateFile(directory, 'vite.main.config.ts');
                    await this.copyTemplateFile(directory, 'vite.preload.config.ts');
                    await this.copyTemplateFile(directory, 'vite.renderer.config.ts');
                    // Copy tsconfig with a small set of presets
                    await this.copyTemplateFile(directory, 'tsconfig.json');
                    // Copy eslint config with recommended settings
                    await this.copyTemplateFile(directory, '.eslintrc.json');
                    // Remove index.js and replace with main.ts
                    await fs_extra_1.default.remove(filePath('index.js'));
                    await this.copyTemplateFile(node_path_1.default.join(directory, 'src'), 'main.ts');
                    await this.copyTemplateFile(node_path_1.default.join(directory, 'src'), 'renderer.ts');
                    // Remove preload.js and replace with preload.ts
                    await fs_extra_1.default.remove(filePath('preload.js'));
                    await this.copyTemplateFile(node_path_1.default.join(directory, 'src'), 'preload.ts');
                    // TODO: Compatible with any path entry.
                    // Vite uses index.html under the root path as the entry point.
                    await fs_extra_1.default.move(filePath('index.html'), node_path_1.default.join(directory, 'index.html'), { overwrite: options.force });
                    await this.updateFileByLine(node_path_1.default.join(directory, 'index.html'), (line) => {
                        if (line.includes('link rel="stylesheet"'))
                            return '';
                        if (line.includes('</body>'))
                            return '    <script type="module" src="/src/renderer.ts"></script>\n  </body>';
                        return line;
                    });
                    // update package.json
                    const packageJSONPath = node_path_1.default.resolve(directory, 'package.json');
                    const packageJSON = await fs_extra_1.default.readJson(packageJSONPath);
                    packageJSON.main = '.vite/build/main.js';
                    // Configure scripts for TS template
                    packageJSON.scripts.lint = 'eslint --ext .ts,.tsx .';
                    await fs_extra_1.default.writeJson(packageJSONPath, packageJSON, {
                        spaces: 2,
                    });
                },
            },
        ];
    }
}
exports.default = new ViteTypeScriptTemplate();
//# sourceMappingURL=data:application/json;base64,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