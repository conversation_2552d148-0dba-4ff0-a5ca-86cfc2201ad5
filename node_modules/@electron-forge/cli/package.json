{"name": "@electron-forge/cli", "version": "7.7.0", "description": "A complete tool for building modern Electron applications", "repository": "https://github.com/electron/forge", "author": "<PERSON>", "license": "MIT", "bin": {"electron-forge": "dist/electron-forge.js", "electron-forge-vscode-nix": "script/vscode.sh", "electron-forge-vscode-win": "script/vscode.cmd"}, "devDependencies": {"@malept/cross-spawn-promise": "^2.0.0", "vitest": "^3.0.3"}, "dependencies": {"@electron-forge/core": "7.7.0", "@electron-forge/core-utils": "7.7.0", "@electron-forge/shared-types": "7.7.0", "@electron/get": "^3.0.0", "chalk": "^4.0.0", "commander": "^11.1.0", "debug": "^4.3.1", "fs-extra": "^10.0.0", "listr2": "^7.0.2", "semver": "^7.2.1"}, "engines": {"node": ">= 16.4.0"}, "funding": [{"type": "individual", "url": "https://github.com/sponsors/malept"}, {"type": "tidelift", "url": "https://tidelift.com/subscription/pkg/npm-.electron-forge-cli?utm_medium=referral&utm_source=npm_fund"}], "publishConfig": {"access": "public"}, "files": ["dist", "src", "script"], "gitHead": "6a88c47b5916a39ee9f993d98d420c6c857de54c"}