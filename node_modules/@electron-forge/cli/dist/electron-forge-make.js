"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getMakeOptions = void 0;
const get_1 = require("@electron/get");
const core_1 = require("@electron-forge/core");
const chalk_1 = __importDefault(require("chalk"));
const commander_1 = require("commander");
require("./util/terminate");
const package_json_1 = __importDefault(require("../package.json"));
const resolve_working_dir_1 = require("./util/resolve-working-dir");
async function getMakeOptions() {
    let workingDir;
    commander_1.program
        .version(package_json_1.default.version, '-V, --version', 'Output the current version.')
        .helpOption('-h, --help', 'Output usage information.')
        .argument('[dir]', 'Directory to run the command in. (default: current directory)')
        .option('--skip-package', `Skip packaging the Electron application, and use the output from a previous ${chalk_1.default.green('package')} run instead.`)
        .option('-a, --arch [arch]', 'Target build architecture.', process.arch)
        .option('-p, --platform [platform]', 'Target build platform.', process.platform)
        .option('--targets [targets]', `Override your ${chalk_1.default.green('make')} targets for this run.`)
        .allowUnknownOption(true)
        .action((dir) => {
        workingDir = (0, resolve_working_dir_1.resolveWorkingDir)(dir, false);
    })
        .parse(process.argv);
    const options = commander_1.program.opts();
    const makeOpts = {
        dir: workingDir,
        interactive: true,
        skipPackage: options.skipPackage,
    };
    if (options.targets)
        makeOpts.overrideTargets = options.targets.split(',');
    if (options.arch)
        makeOpts.arch = options.arch;
    if (options.platform)
        makeOpts.platform = options.platform;
    return makeOpts;
}
exports.getMakeOptions = getMakeOptions;
if (require.main === module) {
    (async () => {
        const makeOpts = await getMakeOptions();
        (0, get_1.initializeProxy)();
        await core_1.api.make(makeOpts);
    })();
}
//# sourceMappingURL=data:application/json;base64,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