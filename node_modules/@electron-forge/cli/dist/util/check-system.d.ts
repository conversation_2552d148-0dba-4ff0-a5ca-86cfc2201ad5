import { ForgeListrTask } from '@electron-forge/shared-types';
export declare function checkPackageManager(): Promise<string>;
type SystemCheckContext = {
    git: boolean;
    node: boolean;
    packageManager: boolean;
};
export declare function checkSystem(callerTask: ForgeListrTask<{
    command: string;
}>): Promise<true | import("listr2").Listr<{
    command: string;
} & SystemCheckContext, any, any>>;
export {};
//# sourceMappingURL=check-system.d.ts.map