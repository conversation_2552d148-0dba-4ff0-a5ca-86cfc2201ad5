"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkSystem = exports.checkPackageManager = void 0;
const node_child_process_1 = require("node:child_process");
const node_os_1 = __importDefault(require("node:os"));
const node_path_1 = __importDefault(require("node:path"));
const core_utils_1 = require("@electron-forge/core-utils");
const debug_1 = __importDefault(require("debug"));
const fs_extra_1 = __importDefault(require("fs-extra"));
const semver_1 = __importDefault(require("semver"));
const d = (0, debug_1.default)('electron-forge:check-system');
async function getGitVersion() {
    return new Promise((resolve) => {
        (0, node_child_process_1.exec)('git --version', (err, output) => (err ? resolve(null) : resolve(output.toString().trim().split(' ').reverse()[0])));
    });
}
async function checkNodeVersion() {
    const { engines } = await fs_extra_1.default.readJson(node_path_1.default.resolve(__dirname, '..', '..', 'package.json'));
    const versionSatisfied = semver_1.default.satisfies(process.versions.node, engines.node);
    if (!versionSatisfied) {
        throw new Error(`You are running Node.js version ${process.versions.node}, but Electron Forge requires Node.js ${engines.node}.`);
    }
    return process.versions.node;
}
/**
 * Packaging an app with Electron Forge requires `node_modules` to be on disk.
 * With `pnpm`, this can be done in a few different ways.
 *
 * `node-linker=hoisted` replicates the behaviour of npm and Yarn Classic, while
 * users may choose to set `public-hoist-pattern` or `hoist-pattern` for advanced
 * configuration purposes.
 */
async function checkPnpmConfig() {
    const { pnpm } = core_utils_1.PACKAGE_MANAGERS;
    const hoistPattern = await (0, core_utils_1.spawnPackageManager)(pnpm, ['config', 'get', 'hoist-pattern']);
    const publicHoistPattern = await (0, core_utils_1.spawnPackageManager)(pnpm, ['config', 'get', 'public-hoist-pattern']);
    if (hoistPattern !== 'undefined' || publicHoistPattern !== 'undefined') {
        d(`Custom hoist pattern detected ${JSON.stringify({
            hoistPattern,
            publicHoistPattern,
        })}, assuming that the user has configured pnpm to package dependencies.`);
        return;
    }
    const nodeLinker = await (0, core_utils_1.spawnPackageManager)(pnpm, ['config', 'get', 'node-linker']);
    if (nodeLinker !== 'hoisted') {
        throw new Error('When using pnpm, `node-linker` must be set to "hoisted" (or a custom `hoist-pattern` or `public-hoist-pattern` must be defined). Run `pnpm config set node-linker hoisted` to set this config value, or add it to your project\'s `.npmrc` file.');
    }
}
// TODO(erickzhao): Drop antiquated versions of npm for Forge v8
const ALLOWLISTED_VERSIONS = {
    npm: {
        all: '^3.0.0 || ^4.0.0 || ~5.1.0 || ~5.2.0 || >= 5.4.2',
        darwin: '>= 5.4.0',
        linux: '>= 5.4.0',
    },
    yarn: {
        all: '>= 1.0.0',
    },
    pnpm: {
        all: '>= 8.0.0',
    },
};
async function checkPackageManager() {
    const pm = await (0, core_utils_1.resolvePackageManager)();
    const version = pm.version ?? (await (0, core_utils_1.spawnPackageManager)(pm, ['--version']));
    const versionString = version.toString().trim();
    const range = ALLOWLISTED_VERSIONS[pm.executable][process.platform] ?? ALLOWLISTED_VERSIONS[pm.executable].all;
    if (!semver_1.default.valid(version)) {
        d(`Invalid semver-string while checking version: ${version}`);
        throw new Error(`Could not check ${pm.executable} version "${version}", assuming incompatible`);
    }
    if (!semver_1.default.satisfies(version, range)) {
        throw new Error(`Incompatible version of ${pm.executable} detected: "${version}" must be in range ${range}`);
    }
    if (pm.executable === 'pnpm') {
        await checkPnpmConfig();
    }
    return `${pm.executable}@${versionString}`;
}
exports.checkPackageManager = checkPackageManager;
/**
 * Some people know their system is OK and don't appreciate the 800ms lag in
 * start up that these checks (in particular the package manager check) costs.
 *
 * Simply creating this flag file in your home directory will skip these checks
 * and shave ~800ms off your forge start time.
 *
 * This is specifically not documented or everyone would make it.
 */
const SKIP_SYSTEM_CHECK = node_path_1.default.resolve(node_os_1.default.homedir(), '.skip-forge-system-check');
async function checkSystem(callerTask) {
    if (!(await fs_extra_1.default.pathExists(SKIP_SYSTEM_CHECK))) {
        d('checking system, create ~/.skip-forge-system-check to stop doing this');
        return callerTask.newListr([
            {
                title: 'Checking git exists',
                // We only call the `initGit` helper in the `init` and `import` commands
                enabled: (ctx) => ctx.command === 'init' || ctx.command === 'import',
                task: async (_, task) => {
                    const gitVersion = await getGitVersion();
                    if (gitVersion) {
                        task.title = `Found git@${gitVersion}`;
                    }
                    else {
                        throw new Error('Could not find git in environment');
                    }
                },
            },
            {
                title: 'Checking node version',
                task: async (_, task) => {
                    const nodeVersion = await checkNodeVersion();
                    task.title = `Found node@${nodeVersion}`;
                },
            },
            {
                title: 'Checking package manager version',
                task: async (_, task) => {
                    const packageManager = await checkPackageManager();
                    task.title = `Found ${packageManager}`;
                },
            },
        ], {
            concurrent: true,
            exitOnError: true,
            rendererOptions: {
                collapseSubtasks: true,
            },
        });
    }
    d('skipping system check');
    return true;
}
exports.checkSystem = checkSystem;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiY2hlY2stc3lzdGVtLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vc3JjL3V0aWwvY2hlY2stc3lzdGVtLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLDJEQUEwQztBQUMxQyxzREFBeUI7QUFDekIsMERBQTZCO0FBRTdCLDJEQUFtSTtBQUVuSSxrREFBMEI7QUFDMUIsd0RBQTBCO0FBQzFCLG9EQUE0QjtBQUU1QixNQUFNLENBQUMsR0FBRyxJQUFBLGVBQUssRUFBQyw2QkFBNkIsQ0FBQyxDQUFDO0FBRS9DLEtBQUssVUFBVSxhQUFhO0lBQzFCLE9BQU8sSUFBSSxPQUFPLENBQWdCLENBQUMsT0FBTyxFQUFFLEVBQUU7UUFDNUMsSUFBQSx5QkFBSSxFQUFDLGVBQWUsRUFBRSxDQUFDLEdBQUcsRUFBRSxNQUFNLEVBQUUsRUFBRSxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxNQUFNLENBQUMsUUFBUSxFQUFFLENBQUMsSUFBSSxFQUFFLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDLE9BQU8sRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO0lBQzVILENBQUMsQ0FBQyxDQUFDO0FBQ0wsQ0FBQztBQUVELEtBQUssVUFBVSxnQkFBZ0I7SUFDN0IsTUFBTSxFQUFFLE9BQU8sRUFBRSxHQUFHLE1BQU0sa0JBQUUsQ0FBQyxRQUFRLENBQUMsbUJBQUksQ0FBQyxPQUFPLENBQUMsU0FBUyxFQUFFLElBQUksRUFBRSxJQUFJLEVBQUUsY0FBYyxDQUFDLENBQUMsQ0FBQztJQUMzRixNQUFNLGdCQUFnQixHQUFHLGdCQUFNLENBQUMsU0FBUyxDQUFDLE9BQU8sQ0FBQyxRQUFRLENBQUMsSUFBSSxFQUFFLE9BQU8sQ0FBQyxJQUFJLENBQUMsQ0FBQztJQUUvRSxJQUFJLENBQUMsZ0JBQWdCLEVBQUUsQ0FBQztRQUN0QixNQUFNLElBQUksS0FBSyxDQUFDLG1DQUFtQyxPQUFPLENBQUMsUUFBUSxDQUFDLElBQUkseUNBQXlDLE9BQU8sQ0FBQyxJQUFJLEdBQUcsQ0FBQyxDQUFDO0lBQ3BJLENBQUM7SUFFRCxPQUFPLE9BQU8sQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDO0FBQy9CLENBQUM7QUFFRDs7Ozs7OztHQU9HO0FBQ0gsS0FBSyxVQUFVLGVBQWU7SUFDNUIsTUFBTSxFQUFFLElBQUksRUFBRSxHQUFHLDZCQUFnQixDQUFDO0lBQ2xDLE1BQU0sWUFBWSxHQUFHLE1BQU0sSUFBQSxnQ0FBbUIsRUFBQyxJQUFJLEVBQUUsQ0FBQyxRQUFRLEVBQUUsS0FBSyxFQUFFLGVBQWUsQ0FBQyxDQUFDLENBQUM7SUFDekYsTUFBTSxrQkFBa0IsR0FBRyxNQUFNLElBQUEsZ0NBQW1CLEVBQUMsSUFBSSxFQUFFLENBQUMsUUFBUSxFQUFFLEtBQUssRUFBRSxzQkFBc0IsQ0FBQyxDQUFDLENBQUM7SUFFdEcsSUFBSSxZQUFZLEtBQUssV0FBVyxJQUFJLGtCQUFrQixLQUFLLFdBQVcsRUFBRSxDQUFDO1FBQ3ZFLENBQUMsQ0FDQyxpQ0FBaUMsSUFBSSxDQUFDLFNBQVMsQ0FBQztZQUM5QyxZQUFZO1lBQ1osa0JBQWtCO1NBQ25CLENBQUMsdUVBQXVFLENBQzFFLENBQUM7UUFDRixPQUFPO0lBQ1QsQ0FBQztJQUVELE1BQU0sVUFBVSxHQUFHLE1BQU0sSUFBQSxnQ0FBbUIsRUFBQyxJQUFJLEVBQUUsQ0FBQyxRQUFRLEVBQUUsS0FBSyxFQUFFLGFBQWEsQ0FBQyxDQUFDLENBQUM7SUFDckYsSUFBSSxVQUFVLEtBQUssU0FBUyxFQUFFLENBQUM7UUFDN0IsTUFBTSxJQUFJLEtBQUssQ0FDYixrUEFBa1AsQ0FDblAsQ0FBQztJQUNKLENBQUM7QUFDSCxDQUFDO0FBRUQsZ0VBQWdFO0FBQ2hFLE1BQU0sb0JBQW9CLEdBQTREO0lBQ3BGLEdBQUcsRUFBRTtRQUNILEdBQUcsRUFBRSxrREFBa0Q7UUFDdkQsTUFBTSxFQUFFLFVBQVU7UUFDbEIsS0FBSyxFQUFFLFVBQVU7S0FDbEI7SUFDRCxJQUFJLEVBQUU7UUFDSixHQUFHLEVBQUUsVUFBVTtLQUNoQjtJQUNELElBQUksRUFBRTtRQUNKLEdBQUcsRUFBRSxVQUFVO0tBQ2hCO0NBQ0YsQ0FBQztBQUVLLEtBQUssVUFBVSxtQkFBbUI7SUFDdkMsTUFBTSxFQUFFLEdBQUcsTUFBTSxJQUFBLGtDQUFxQixHQUFFLENBQUM7SUFDekMsTUFBTSxPQUFPLEdBQUcsRUFBRSxDQUFDLE9BQU8sSUFBSSxDQUFDLE1BQU0sSUFBQSxnQ0FBbUIsRUFBQyxFQUFFLEVBQUUsQ0FBQyxXQUFXLENBQUMsQ0FBQyxDQUFDLENBQUM7SUFDN0UsTUFBTSxhQUFhLEdBQUcsT0FBTyxDQUFDLFFBQVEsRUFBRSxDQUFDLElBQUksRUFBRSxDQUFDO0lBRWhELE1BQU0sS0FBSyxHQUFHLG9CQUFvQixDQUFDLEVBQUUsQ0FBQyxVQUFVLENBQUMsQ0FBQyxPQUFPLENBQUMsUUFBUSxDQUFDLElBQUksb0JBQW9CLENBQUMsRUFBRSxDQUFDLFVBQVUsQ0FBQyxDQUFDLEdBQUcsQ0FBQztJQUMvRyxJQUFJLENBQUMsZ0JBQU0sQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLEVBQUUsQ0FBQztRQUMzQixDQUFDLENBQUMsaURBQWlELE9BQU8sRUFBRSxDQUFDLENBQUM7UUFDOUQsTUFBTSxJQUFJLEtBQUssQ0FBQyxtQkFBbUIsRUFBRSxDQUFDLFVBQVUsYUFBYSxPQUFPLDBCQUEwQixDQUFDLENBQUM7SUFDbEcsQ0FBQztJQUNELElBQUksQ0FBQyxnQkFBTSxDQUFDLFNBQVMsQ0FBQyxPQUFPLEVBQUUsS0FBSyxDQUFDLEVBQUUsQ0FBQztRQUN0QyxNQUFNLElBQUksS0FBSyxDQUFDLDJCQUEyQixFQUFFLENBQUMsVUFBVSxlQUFlLE9BQU8sc0JBQXNCLEtBQUssRUFBRSxDQUFDLENBQUM7SUFDL0csQ0FBQztJQUVELElBQUksRUFBRSxDQUFDLFVBQVUsS0FBSyxNQUFNLEVBQUUsQ0FBQztRQUM3QixNQUFNLGVBQWUsRUFBRSxDQUFDO0lBQzFCLENBQUM7SUFFRCxPQUFPLEdBQUcsRUFBRSxDQUFDLFVBQVUsSUFBSSxhQUFhLEVBQUUsQ0FBQztBQUM3QyxDQUFDO0FBbkJELGtEQW1CQztBQUVEOzs7Ozs7OztHQVFHO0FBQ0gsTUFBTSxpQkFBaUIsR0FBRyxtQkFBSSxDQUFDLE9BQU8sQ0FBQyxpQkFBRSxDQUFDLE9BQU8sRUFBRSxFQUFFLDBCQUEwQixDQUFDLENBQUM7QUFRMUUsS0FBSyxVQUFVLFdBQVcsQ0FBQyxVQUErQztJQUMvRSxJQUFJLENBQUMsQ0FBQyxNQUFNLGtCQUFFLENBQUMsVUFBVSxDQUFDLGlCQUFpQixDQUFDLENBQUMsRUFBRSxDQUFDO1FBQzlDLENBQUMsQ0FBQyx1RUFBdUUsQ0FBQyxDQUFDO1FBQzNFLE9BQU8sVUFBVSxDQUFDLFFBQVEsQ0FDeEI7WUFDRTtnQkFDRSxLQUFLLEVBQUUscUJBQXFCO2dCQUM1Qix3RUFBd0U7Z0JBQ3hFLE9BQU8sRUFBRSxDQUFDLEdBQUcsRUFBVyxFQUFFLENBQUMsR0FBRyxDQUFDLE9BQU8sS0FBSyxNQUFNLElBQUksR0FBRyxDQUFDLE9BQU8sS0FBSyxRQUFRO2dCQUM3RSxJQUFJLEVBQUUsS0FBSyxFQUFFLENBQUMsRUFBRSxJQUFJLEVBQUUsRUFBRTtvQkFDdEIsTUFBTSxVQUFVLEdBQUcsTUFBTSxhQUFhLEVBQUUsQ0FBQztvQkFDekMsSUFBSSxVQUFVLEVBQUUsQ0FBQzt3QkFDZixJQUFJLENBQUMsS0FBSyxHQUFHLGFBQWEsVUFBVSxFQUFFLENBQUM7b0JBQ3pDLENBQUM7eUJBQU0sQ0FBQzt3QkFDTixNQUFNLElBQUksS0FBSyxDQUFDLG1DQUFtQyxDQUFDLENBQUM7b0JBQ3ZELENBQUM7Z0JBQ0gsQ0FBQzthQUNGO1lBQ0Q7Z0JBQ0UsS0FBSyxFQUFFLHVCQUF1QjtnQkFDOUIsSUFBSSxFQUFFLEtBQUssRUFBRSxDQUFDLEVBQUUsSUFBSSxFQUFFLEVBQUU7b0JBQ3RCLE1BQU0sV0FBVyxHQUFHLE1BQU0sZ0JBQWdCLEVBQUUsQ0FBQztvQkFDN0MsSUFBSSxDQUFDLEtBQUssR0FBRyxjQUFjLFdBQVcsRUFBRSxDQUFDO2dCQUMzQyxDQUFDO2FBQ0Y7WUFDRDtnQkFDRSxLQUFLLEVBQUUsa0NBQWtDO2dCQUN6QyxJQUFJLEVBQUUsS0FBSyxFQUFFLENBQUMsRUFBRSxJQUFJLEVBQUUsRUFBRTtvQkFDdEIsTUFBTSxjQUFjLEdBQUcsTUFBTSxtQkFBbUIsRUFBRSxDQUFDO29CQUNuRCxJQUFJLENBQUMsS0FBSyxHQUFHLFNBQVMsY0FBYyxFQUFFLENBQUM7Z0JBQ3pDLENBQUM7YUFDRjtTQUNGLEVBQ0Q7WUFDRSxVQUFVLEVBQUUsSUFBSTtZQUNoQixXQUFXLEVBQUUsSUFBSTtZQUNqQixlQUFlLEVBQUU7Z0JBQ2YsZ0JBQWdCLEVBQUUsSUFBSTthQUN2QjtTQUNGLENBQ0YsQ0FBQztJQUNKLENBQUM7SUFDRCxDQUFDLENBQUMsdUJBQXVCLENBQUMsQ0FBQztJQUMzQixPQUFPLElBQUksQ0FBQztBQUNkLENBQUM7QUE1Q0Qsa0NBNENDIn0=