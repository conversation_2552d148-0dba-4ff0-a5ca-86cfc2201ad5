/**
 * Resolves the directory in which to use a CLI command.
 * @param dir - The directory specified by the user (can be relative or absolute)
 * @param checkExisting - Checks if the directory exists. If true and directory is non-existent, it will fall back to the current working directory
 * @returns
 */
export declare function resolveWorkingDir(dir: string, checkExisting?: boolean): string;
//# sourceMappingURL=resolve-working-dir.d.ts.map