"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const get_1 = require("@electron/get");
const core_1 = require("@electron-forge/core");
const chalk_1 = __importDefault(require("chalk"));
const commander_1 = require("commander");
require("./util/terminate");
const package_json_1 = __importDefault(require("../package.json"));
const electron_forge_make_1 = require("./electron-forge-make");
const resolve_working_dir_1 = require("./util/resolve-working-dir");
commander_1.program
    .version(package_json_1.default.version, '-V, --version', 'Output the current version.')
    .helpOption('-h, --help', 'Output usage information.')
    .argument('[dir]', 'Directory to run the command in. (default: current directory)')
    .option('--target [target[,target...]]', 'A comma-separated list of deployment targets. (default: all publishers in your Forge config)')
    .option('--dry-run', `Run the ${chalk_1.default.green('make')} command and save publish metadata without uploading anything.`)
    .option('--from-dry-run', 'Publish artifacts from the last saved dry run.')
    .allowUnknownOption(true)
    .action(async (targetDir) => {
    const dir = (0, resolve_working_dir_1.resolveWorkingDir)(targetDir);
    const options = commander_1.program.opts();
    (0, get_1.initializeProxy)();
    const publishOpts = {
        dir,
        interactive: true,
        dryRun: options.dryRun,
        dryRunResume: options.fromDryRun,
    };
    if (options.target)
        publishOpts.publishTargets = options.target.split(',');
    publishOpts.makeOptions = await (0, electron_forge_make_1.getMakeOptions)();
    await core_1.api.publish(publishOpts);
})
    .parse(process.argv);
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZWxlY3Ryb24tZm9yZ2UtcHVibGlzaC5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uL3NyYy9lbGVjdHJvbi1mb3JnZS1wdWJsaXNoLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsdUNBQWdEO0FBQ2hELCtDQUEyRDtBQUMzRCxrREFBMEI7QUFDMUIseUNBQW9DO0FBRXBDLDRCQUEwQjtBQUMxQixtRUFBMEM7QUFFMUMsK0RBQXVEO0FBQ3ZELG9FQUErRDtBQUUvRCxtQkFBTztLQUNKLE9BQU8sQ0FBQyxzQkFBVyxDQUFDLE9BQU8sRUFBRSxlQUFlLEVBQUUsNkJBQTZCLENBQUM7S0FDNUUsVUFBVSxDQUFDLFlBQVksRUFBRSwyQkFBMkIsQ0FBQztLQUNyRCxRQUFRLENBQUMsT0FBTyxFQUFFLCtEQUErRCxDQUFDO0tBQ2xGLE1BQU0sQ0FBQywrQkFBK0IsRUFBRSw4RkFBOEYsQ0FBQztLQUN2SSxNQUFNLENBQUMsV0FBVyxFQUFFLFdBQVcsZUFBSyxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsZ0VBQWdFLENBQUM7S0FDbkgsTUFBTSxDQUFDLGdCQUFnQixFQUFFLGdEQUFnRCxDQUFDO0tBQzFFLGtCQUFrQixDQUFDLElBQUksQ0FBQztLQUN4QixNQUFNLENBQUMsS0FBSyxFQUFFLFNBQVMsRUFBRSxFQUFFO0lBQzFCLE1BQU0sR0FBRyxHQUFHLElBQUEsdUNBQWlCLEVBQUMsU0FBUyxDQUFDLENBQUM7SUFDekMsTUFBTSxPQUFPLEdBQUcsbUJBQU8sQ0FBQyxJQUFJLEVBQUUsQ0FBQztJQUUvQixJQUFBLHFCQUFlLEdBQUUsQ0FBQztJQUVsQixNQUFNLFdBQVcsR0FBbUI7UUFDbEMsR0FBRztRQUNILFdBQVcsRUFBRSxJQUFJO1FBQ2pCLE1BQU0sRUFBRSxPQUFPLENBQUMsTUFBTTtRQUN0QixZQUFZLEVBQUUsT0FBTyxDQUFDLFVBQVU7S0FDakMsQ0FBQztJQUNGLElBQUksT0FBTyxDQUFDLE1BQU07UUFBRSxXQUFXLENBQUMsY0FBYyxHQUFHLE9BQU8sQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDO0lBRTNFLFdBQVcsQ0FBQyxXQUFXLEdBQUcsTUFBTSxJQUFBLG9DQUFjLEdBQUUsQ0FBQztJQUVqRCxNQUFNLFVBQUcsQ0FBQyxPQUFPLENBQUMsV0FBVyxDQUFDLENBQUM7QUFDakMsQ0FBQyxDQUFDO0tBQ0QsS0FBSyxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsQ0FBQyJ9