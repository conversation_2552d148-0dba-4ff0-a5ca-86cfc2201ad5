"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@electron-forge/core");
const commander_1 = require("commander");
require("./util/terminate");
const package_json_1 = __importDefault(require("../package.json"));
const resolve_working_dir_1 = require("./util/resolve-working-dir");
(async () => {
    let commandArgs = process.argv;
    let appArgs;
    const doubleDashIndex = process.argv.indexOf('--');
    if (doubleDashIndex !== -1) {
        commandArgs = process.argv.slice(0, doubleDashIndex);
        appArgs = process.argv.slice(doubleDashIndex + 1);
    }
    let dir;
    commander_1.program
        .version(package_json_1.default.version, '-V, --version', 'Output the current version.')
        .helpOption('-h, --help', 'Output usage information.')
        .argument('[dir]', 'Directory to run the command in. (default: current directory)')
        .option('-p, --app-path <path>', 'Path to the Electron app to launch. (default: current directory)')
        .option('-l, --enable-logging', 'Enable internal Electron logging.')
        .option('-n, --run-as-node', 'Run the Electron app as a Node.JS script.')
        .addOption(new commander_1.Option('--vscode').hideHelp()) // Used to enable arg transformation for debugging Electron through VSCode. Hidden from users.
        .option('-i, --inspect-electron', 'Run Electron in inspect mode to allow debugging the main process.')
        .option('--inspect-brk-electron', 'Run Electron in inspect-brk mode to allow debugging the main process.')
        .addHelpText('after', `
      Any arguments found after "--" will be passed to the Electron app. For example...
      
          $ npx electron-forge start /path/to/project --enable-logging -- -d -f foo.txt
                                    
      ...will pass the arguments "-d -f foo.txt" to the Electron app.`)
        .passThroughOptions(true) // allows args to be passed down to the Electron executable
        .action((targetDir) => {
        dir = (0, resolve_working_dir_1.resolveWorkingDir)(targetDir);
    })
        .parse(commandArgs);
    const options = commander_1.program.opts();
    const opts = {
        dir,
        interactive: true,
        enableLogging: !!options.enableLogging,
        runAsNode: !!options.runAsNode,
        inspect: !!options.inspectElectron,
        inspectBrk: !!options.inspectBrkElectron,
    };
    if (options.vscode && appArgs) {
        // Args are in the format ~arg~ so we need to strip the "~"
        appArgs = appArgs.map((arg) => arg.substr(1, arg.length - 2)).filter((arg) => arg.length > 0);
    }
    if (options.appPath)
        opts.appPath = options.appPath;
    if (appArgs)
        opts.args = appArgs;
    const spawned = await core_1.api.start(opts);
    await new Promise((resolve) => {
        const listenForExit = (child) => {
            // Why: changing to const causes TypeScript compilation to fail.
            /* eslint-disable prefer-const */
            let onExit;
            let onRestart;
            /* eslint-enable prefer-const */
            const removeListeners = () => {
                child.removeListener('exit', onExit);
                child.removeListener('restarted', onRestart);
            };
            onExit = (code) => {
                removeListeners();
                if (spawned.restarted)
                    return;
                if (code !== 0) {
                    process.exit(code);
                }
                resolve();
            };
            onRestart = (newChild) => {
                removeListeners();
                listenForExit(newChild);
            };
            child.on('exit', onExit);
            child.on('restarted', onRestart);
        };
        listenForExit(spawned);
    });
})();
//# sourceMappingURL=data:application/json;base64,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