"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@electron-forge/core");
const commander_1 = require("commander");
require("./util/terminate");
const package_json_1 = __importDefault(require("../package.json"));
const resolve_working_dir_1 = require("./util/resolve-working-dir");
commander_1.program
    .version(package_json_1.default.version, '-V, --version', 'Output the current version.')
    .helpOption('-h, --help', 'Output usage information.')
    .argument('[dir]', 'Directory of the project to import. (default: current directory)')
    .action(async (dir) => {
    const workingDir = (0, resolve_working_dir_1.resolveWorkingDir)(dir, false);
    await core_1.api.import({
        dir: workingDir,
        interactive: true,
    });
})
    .parse(process.argv);
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZWxlY3Ryb24tZm9yZ2UtaW1wb3J0LmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vc3JjL2VsZWN0cm9uLWZvcmdlLWltcG9ydC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7OztBQUFBLCtDQUEyQztBQUMzQyx5Q0FBb0M7QUFFcEMsNEJBQTBCO0FBQzFCLG1FQUEwQztBQUUxQyxvRUFBK0Q7QUFFL0QsbUJBQU87S0FDSixPQUFPLENBQUMsc0JBQVcsQ0FBQyxPQUFPLEVBQUUsZUFBZSxFQUFFLDZCQUE2QixDQUFDO0tBQzVFLFVBQVUsQ0FBQyxZQUFZLEVBQUUsMkJBQTJCLENBQUM7S0FDckQsUUFBUSxDQUFDLE9BQU8sRUFBRSxrRUFBa0UsQ0FBQztLQUNyRixNQUFNLENBQUMsS0FBSyxFQUFFLEdBQVcsRUFBRSxFQUFFO0lBQzVCLE1BQU0sVUFBVSxHQUFHLElBQUEsdUNBQWlCLEVBQUMsR0FBRyxFQUFFLEtBQUssQ0FBQyxDQUFDO0lBQ2pELE1BQU0sVUFBRyxDQUFDLE1BQU0sQ0FBQztRQUNmLEdBQUcsRUFBRSxVQUFVO1FBQ2YsV0FBVyxFQUFFLElBQUk7S0FDbEIsQ0FBQyxDQUFDO0FBQ0wsQ0FBQyxDQUFDO0tBQ0QsS0FBSyxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsQ0FBQyJ9