#!/usr/bin/env node
"use strict";
// This file requires a shebang above. If it is missing, this is an error.
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const commander_1 = require("commander");
const listr2_1 = require("listr2");
require("./util/terminate");
const package_json_1 = __importDefault(require("../package.json"));
const check_system_1 = require("./util/check-system");
commander_1.program
    .version(package_json_1.default.version, '-V, --version', 'Output the current version.')
    .helpOption('-h, --help', 'Output usage information.')
    .command('init', 'Initialize a new Electron application.')
    .command('import', 'Import an existing Electron project to Forge.')
    .command('start', 'Start the current Electron application in development mode.')
    .command('package', 'Package the current Electron application.')
    .command('make', 'Generate distributables for the current Electron application.')
    .command('publish', 'Publish the current Electron application.')
    .hook('preSubcommand', async (_command, subcommand) => {
    if (!process.argv.includes('--help') && !process.argv.includes('-h')) {
        const runner = new listr2_1.Listr([
            {
                title: 'Checking your system',
                task: async (ctx, task) => {
                    ctx.command = subcommand.name();
                    return await (0, check_system_1.checkSystem)(task);
                },
            },
        ], {
            concurrent: false,
            exitOnError: true,
            fallbackRendererCondition: Boolean(process.env.DEBUG) || Boolean(process.env.CI),
        });
        try {
            await runner.run();
        }
        catch {
            process.exit(1);
        }
    }
});
commander_1.program.parse(process.argv);
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZWxlY3Ryb24tZm9yZ2UuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi9zcmMvZWxlY3Ryb24tZm9yZ2UudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7QUFDQSwwRUFBMEU7Ozs7O0FBRTFFLHlDQUFvQztBQUNwQyxtQ0FBK0I7QUFFL0IsNEJBQTBCO0FBRTFCLG1FQUEwQztBQUUxQyxzREFBa0Q7QUFFbEQsbUJBQU87S0FDSixPQUFPLENBQUMsc0JBQVcsQ0FBQyxPQUFPLEVBQUUsZUFBZSxFQUFFLDZCQUE2QixDQUFDO0tBQzVFLFVBQVUsQ0FBQyxZQUFZLEVBQUUsMkJBQTJCLENBQUM7S0FDckQsT0FBTyxDQUFDLE1BQU0sRUFBRSx3Q0FBd0MsQ0FBQztLQUN6RCxPQUFPLENBQUMsUUFBUSxFQUFFLCtDQUErQyxDQUFDO0tBQ2xFLE9BQU8sQ0FBQyxPQUFPLEVBQUUsNkRBQTZELENBQUM7S0FDL0UsT0FBTyxDQUFDLFNBQVMsRUFBRSwyQ0FBMkMsQ0FBQztLQUMvRCxPQUFPLENBQUMsTUFBTSxFQUFFLCtEQUErRCxDQUFDO0tBQ2hGLE9BQU8sQ0FBQyxTQUFTLEVBQUUsMkNBQTJDLENBQUM7S0FDL0QsSUFBSSxDQUFDLGVBQWUsRUFBRSxLQUFLLEVBQUUsUUFBUSxFQUFFLFVBQVUsRUFBRSxFQUFFO0lBQ3BELElBQUksQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUM7UUFDckUsTUFBTSxNQUFNLEdBQUcsSUFBSSxjQUFLLENBR3RCO1lBQ0U7Z0JBQ0UsS0FBSyxFQUFFLHNCQUFzQjtnQkFDN0IsSUFBSSxFQUFFLEtBQUssRUFBRSxHQUFHLEVBQUUsSUFBSSxFQUFFLEVBQUU7b0JBQ3hCLEdBQUcsQ0FBQyxPQUFPLEdBQUcsVUFBVSxDQUFDLElBQUksRUFBRSxDQUFDO29CQUNoQyxPQUFPLE1BQU0sSUFBQSwwQkFBVyxFQUFDLElBQUksQ0FBQyxDQUFDO2dCQUNqQyxDQUFDO2FBQ0Y7U0FDRixFQUNEO1lBQ0UsVUFBVSxFQUFFLEtBQUs7WUFDakIsV0FBVyxFQUFFLElBQUk7WUFDakIseUJBQXlCLEVBQUUsT0FBTyxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsS0FBSyxDQUFDLElBQUksT0FBTyxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDO1NBQ2pGLENBQ0YsQ0FBQztRQUVGLElBQUksQ0FBQztZQUNILE1BQU0sTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDO1FBQ3JCLENBQUM7UUFBQyxNQUFNLENBQUM7WUFDUCxPQUFPLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ2xCLENBQUM7SUFDSCxDQUFDO0FBQ0gsQ0FBQyxDQUFDLENBQUM7QUFFTCxtQkFBTyxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLENBQUMifQ==