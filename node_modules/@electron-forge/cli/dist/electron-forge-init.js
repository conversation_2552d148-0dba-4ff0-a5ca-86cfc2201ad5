"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@electron-forge/core");
const commander_1 = require("commander");
require("./util/terminate");
const package_json_1 = __importDefault(require("../package.json"));
const resolve_working_dir_1 = require("./util/resolve-working-dir");
commander_1.program
    .version(package_json_1.default.version, '-V, --version', 'Output the current version.')
    .helpOption('-h, --help', 'Output usage information.')
    .argument('[dir]', 'Directory to initialize the project in. (default: current directory)')
    .option('-t, --template [name]', 'Name of the Forge template to use.', 'base')
    .option('-c, --copy-ci-files', 'Whether to copy the templated CI files.', false)
    .option('-f, --force', 'Whether to overwrite an existing directory.', false)
    .action(async (dir) => {
    const workingDir = (0, resolve_working_dir_1.resolveWorkingDir)(dir, false);
    const options = commander_1.program.opts();
    const initOpts = {
        dir: workingDir,
        interactive: true,
        copyCIFiles: !!options.copyCiFiles,
        force: !!options.force,
    };
    if (options.template)
        initOpts.template = options.template;
    await core_1.api.init(initOpts);
})
    .parse(process.argv);
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZWxlY3Ryb24tZm9yZ2UtaW5pdC5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uL3NyYy9lbGVjdHJvbi1mb3JnZS1pbml0LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsK0NBQXdEO0FBQ3hELHlDQUFvQztBQUVwQyw0QkFBMEI7QUFDMUIsbUVBQTBDO0FBRTFDLG9FQUErRDtBQUUvRCxtQkFBTztLQUNKLE9BQU8sQ0FBQyxzQkFBVyxDQUFDLE9BQU8sRUFBRSxlQUFlLEVBQUUsNkJBQTZCLENBQUM7S0FDNUUsVUFBVSxDQUFDLFlBQVksRUFBRSwyQkFBMkIsQ0FBQztLQUNyRCxRQUFRLENBQUMsT0FBTyxFQUFFLHNFQUFzRSxDQUFDO0tBQ3pGLE1BQU0sQ0FBQyx1QkFBdUIsRUFBRSxvQ0FBb0MsRUFBRSxNQUFNLENBQUM7S0FDN0UsTUFBTSxDQUFDLHFCQUFxQixFQUFFLHlDQUF5QyxFQUFFLEtBQUssQ0FBQztLQUMvRSxNQUFNLENBQUMsYUFBYSxFQUFFLDZDQUE2QyxFQUFFLEtBQUssQ0FBQztLQUMzRSxNQUFNLENBQUMsS0FBSyxFQUFFLEdBQUcsRUFBRSxFQUFFO0lBQ3BCLE1BQU0sVUFBVSxHQUFHLElBQUEsdUNBQWlCLEVBQUMsR0FBRyxFQUFFLEtBQUssQ0FBQyxDQUFDO0lBRWpELE1BQU0sT0FBTyxHQUFHLG1CQUFPLENBQUMsSUFBSSxFQUFFLENBQUM7SUFFL0IsTUFBTSxRQUFRLEdBQWdCO1FBQzVCLEdBQUcsRUFBRSxVQUFVO1FBQ2YsV0FBVyxFQUFFLElBQUk7UUFDakIsV0FBVyxFQUFFLENBQUMsQ0FBQyxPQUFPLENBQUMsV0FBVztRQUNsQyxLQUFLLEVBQUUsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxLQUFLO0tBQ3ZCLENBQUM7SUFDRixJQUFJLE9BQU8sQ0FBQyxRQUFRO1FBQUUsUUFBUSxDQUFDLFFBQVEsR0FBRyxPQUFPLENBQUMsUUFBUSxDQUFDO0lBRTNELE1BQU0sVUFBRyxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQztBQUMzQixDQUFDLENBQUM7S0FDRCxLQUFLLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxDQUFDIn0=