"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const cross_spawn_promise_1 = require("@malept/cross-spawn-promise");
const debug_1 = __importDefault(require("debug"));
const username_1 = __importDefault(require("username"));
const d = (0, debug_1.default)('electron-forge:determine-author');
async function getGitConfig(name, cwd) {
    const value = await (0, cross_spawn_promise_1.spawn)('git', ['config', '--get', name], { cwd });
    return value.trim();
}
const getAuthorFromGitConfig = async (dir) => {
    try {
        const name = await getGitConfig('user.name', dir);
        const email = await getGitConfig('user.email', dir);
        return { name, email };
    }
    catch (err) {
        d('Error when getting git config:', err);
        return undefined;
    }
};
exports.default = async (dir) => (await getAuthorFromGitConfig(dir)) || (0, username_1.default)();
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZGV0ZXJtaW5lLWF1dGhvci5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uL3NyYy9kZXRlcm1pbmUtYXV0aG9yLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7O0FBQ0EscUVBQW9EO0FBQ3BELGtEQUEwQjtBQUMxQix3REFBZ0M7QUFFaEMsTUFBTSxDQUFDLEdBQUcsSUFBQSxlQUFLLEVBQUMsaUNBQWlDLENBQUMsQ0FBQztBQUVuRCxLQUFLLFVBQVUsWUFBWSxDQUFDLElBQVksRUFBRSxHQUFXO0lBQ25ELE1BQU0sS0FBSyxHQUFHLE1BQU0sSUFBQSwyQkFBSyxFQUFDLEtBQUssRUFBRSxDQUFDLFFBQVEsRUFBRSxPQUFPLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRSxHQUFHLEVBQUUsQ0FBQyxDQUFDO0lBQ3JFLE9BQU8sS0FBSyxDQUFDLElBQUksRUFBRSxDQUFDO0FBQ3RCLENBQUM7QUFFRCxNQUFNLHNCQUFzQixHQUFHLEtBQUssRUFBRSxHQUFXLEVBQTBCLEVBQUU7SUFDM0UsSUFBSSxDQUFDO1FBQ0gsTUFBTSxJQUFJLEdBQUcsTUFBTSxZQUFZLENBQUMsV0FBVyxFQUFFLEdBQUcsQ0FBQyxDQUFDO1FBQ2xELE1BQU0sS0FBSyxHQUFHLE1BQU0sWUFBWSxDQUFDLFlBQVksRUFBRSxHQUFHLENBQUMsQ0FBQztRQUNwRCxPQUFPLEVBQUUsSUFBSSxFQUFFLEtBQUssRUFBRSxDQUFDO0lBQ3pCLENBQUM7SUFBQyxPQUFPLEdBQUcsRUFBRSxDQUFDO1FBQ2IsQ0FBQyxDQUFDLGdDQUFnQyxFQUFFLEdBQUcsQ0FBQyxDQUFDO1FBQ3pDLE9BQU8sU0FBUyxDQUFDO0lBQ25CLENBQUM7QUFDSCxDQUFDLENBQUM7QUFFRixrQkFBZSxLQUFLLEVBQUUsR0FBVyxFQUEwQixFQUFFLENBQUMsQ0FBQyxNQUFNLHNCQUFzQixDQUFDLEdBQUcsQ0FBQyxDQUFDLElBQUksSUFBQSxrQkFBUSxHQUFFLENBQUMifQ==