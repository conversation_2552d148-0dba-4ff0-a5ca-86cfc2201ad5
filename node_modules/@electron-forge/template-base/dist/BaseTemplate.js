"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseTemplate = void 0;
const node_path_1 = __importDefault(require("node:path"));
const core_utils_1 = require("@electron-forge/core-utils");
const debug_1 = __importDefault(require("debug"));
const fs_extra_1 = __importDefault(require("fs-extra"));
const determine_author_1 = __importDefault(require("./determine-author"));
// eslint-disable-next-line @typescript-eslint/no-require-imports
const currentForgeVersion = require('../package.json').version;
const d = (0, debug_1.default)('electron-forge:template:base');
const tmplDir = node_path_1.default.resolve(__dirname, '../tmpl');
class BaseTemplate {
    constructor() {
        this.templateDir = tmplDir;
        this.requiredForgeVersion = currentForgeVersion;
    }
    get dependencies() {
        const packageJSONPath = node_path_1.default.join(this.templateDir, 'package.json');
        if (fs_extra_1.default.pathExistsSync(packageJSONPath)) {
            const deps = fs_extra_1.default.readJsonSync(packageJSONPath).dependencies;
            if (deps) {
                return Object.entries(deps).map(([packageName, version]) => {
                    if (version === 'ELECTRON_FORGE/VERSION') {
                        version = `^${currentForgeVersion}`;
                    }
                    return `${packageName}@${version}`;
                });
            }
        }
        return [];
    }
    get devDependencies() {
        const packageJSONPath = node_path_1.default.join(this.templateDir, 'package.json');
        if (fs_extra_1.default.pathExistsSync(packageJSONPath)) {
            const packageDevDeps = fs_extra_1.default.readJsonSync(packageJSONPath).devDependencies;
            if (packageDevDeps) {
                return Object.entries(packageDevDeps).map(([packageName, version]) => {
                    if (version === 'ELECTRON_FORGE/VERSION') {
                        version = `^${currentForgeVersion}`;
                    }
                    return `${packageName}@${version}`;
                });
            }
        }
        return [];
    }
    async initializeTemplate(directory, { copyCIFiles }) {
        return [
            {
                title: 'Copying starter files',
                task: async () => {
                    const pm = await (0, core_utils_1.resolvePackageManager)();
                    d('creating directory:', node_path_1.default.resolve(directory, 'src'));
                    await fs_extra_1.default.mkdirs(node_path_1.default.resolve(directory, 'src'));
                    const rootFiles = ['_gitignore', 'forge.config.js'];
                    if (pm.executable === 'pnpm') {
                        rootFiles.push('.npmrc');
                    }
                    if (copyCIFiles) {
                        d(`Copying CI files is currently not supported - this will be updated in a later version of Forge`);
                    }
                    const srcFiles = ['index.css', 'index.js', 'index.html', 'preload.js'];
                    for (const file of rootFiles) {
                        await this.copy(node_path_1.default.resolve(tmplDir, file), node_path_1.default.resolve(directory, file.replace(/^_/, '.')));
                    }
                    for (const file of srcFiles) {
                        await this.copy(node_path_1.default.resolve(tmplDir, file), node_path_1.default.resolve(directory, 'src', file));
                    }
                },
            },
            {
                title: 'Initializing package.json',
                task: async () => {
                    await this.initializePackageJSON(directory);
                },
            },
        ];
    }
    async copy(source, target) {
        d(`copying "${source}" --> "${target}"`);
        await fs_extra_1.default.copy(source, target);
    }
    async copyTemplateFile(destDir, basename) {
        await this.copy(node_path_1.default.join(this.templateDir, basename), node_path_1.default.resolve(destDir, basename));
    }
    async initializePackageJSON(directory) {
        const packageJSON = await fs_extra_1.default.readJson(node_path_1.default.resolve(__dirname, '../tmpl/package.json'));
        packageJSON.productName = packageJSON.name = node_path_1.default.basename(directory).toLowerCase();
        packageJSON.author = await (0, determine_author_1.default)(directory);
        const pm = await (0, core_utils_1.resolvePackageManager)();
        // As of pnpm v10, no postinstall scripts will run unless allowlisted through `onlyBuiltDependencies`
        if (pm.executable === 'pnpm') {
            packageJSON.pnpm = {
                onlyBuiltDependencies: ['electron'],
            };
        }
        packageJSON.scripts.lint = 'echo "No linting configured"';
        d('writing package.json to:', directory);
        await fs_extra_1.default.writeJson(node_path_1.default.resolve(directory, 'package.json'), packageJSON, { spaces: 2 });
    }
    async updateFileByLine(inputPath, lineHandler, outputPath) {
        const fileContents = (await fs_extra_1.default.readFile(inputPath, 'utf8')).split('\n').map(lineHandler).join('\n');
        await fs_extra_1.default.writeFile(outputPath || inputPath, fileContents);
        if (outputPath !== undefined) {
            await fs_extra_1.default.remove(inputPath);
        }
    }
}
exports.BaseTemplate = BaseTemplate;
exports.default = new BaseTemplate();
//# sourceMappingURL=data:application/json;base64,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