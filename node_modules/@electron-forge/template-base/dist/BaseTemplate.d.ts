import { ForgeListrTaskDefinition, ForgeTemplate, InitTemplateOptions } from '@electron-forge/shared-types';
export declare class BaseTemplate implements ForgeTemplate {
    templateDir: string;
    requiredForgeVersion: any;
    get dependencies(): string[];
    get devDependencies(): string[];
    initializeTemplate(directory: string, { copyCIFiles }: InitTemplateOptions): Promise<ForgeListrTaskDefinition[]>;
    copy(source: string, target: string): Promise<void>;
    copyTemplateFile(destDir: string, basename: string): Promise<void>;
    initializePackageJSON(directory: string): Promise<void>;
    updateFileByLine(inputPath: string, lineHandler: (line: string) => string, outputPath?: string | undefined): Promise<void>;
}
declare const _default: BaseTemplate;
export default _default;
//# sourceMappingURL=BaseTemplate.d.ts.map